document.addEventListener('DOMContentLoaded', function() {
    initializeSidebar();
});

function initializeSidebar() {
    try {
        const sidebarElements = {
            toggle: document.querySelector('.sidebar-toggle'),
            sidebar: document.querySelector('.sidebar'),
            content: document.querySelector('.content'),
            icon: document.querySelector('.toggle-icon'),
            menuItems: document.querySelectorAll('.menu-item[data-toggle="submenu"]'),
            allMenuItems: document.querySelectorAll('.menu-item')
        };

        if (!sidebarElements.toggle || !sidebarElements.sidebar || !sidebarElements.content) {
            console.warn('Required sidebar elements not found');
            return;
        }

        function toggleSidebar(show) {
            try {
                if (show === undefined) {
                    sidebarElements.sidebar.classList.toggle('sidebar-hidden');
                    sidebarElements.content.classList.toggle('full-width');
                    sidebarElements.toggle.classList.toggle('active');

                    if (sidebarElements.icon) {
                        sidebarElements.icon.classList.toggle('bi-list');
                        sidebarElements.icon.classList.toggle('bi-x');
                    }

                    // Mover el botón 100px a la derecha si el sidebar está visible
                    if (!sidebarElements.sidebar.classList.contains('sidebar-hidden')) {
                        sidebarElements.toggle.style.left = '253px'; // Mueve el botón 100px a la derecha
                    } else {
                        sidebarElements.toggle.style.left = '15px'; // Regresa a la posición original
                    }
                } else {
                    sidebarElements.sidebar.classList.toggle('sidebar-hidden', !show);
                    sidebarElements.content.classList.toggle('full-width', !show);
                    sidebarElements.toggle.classList.toggle('active', show);

                    if (sidebarElements.icon) {
                        sidebarElements.icon.classList.toggle('bi-list', !show);
                        sidebarElements.icon.classList.toggle('bi-x', show);
                    }

                    // Actualiza la posición del botón según el estado
                    sidebarElements.toggle.style.left = show ? '253px' : '15px';
                }
            } catch (error) {
                console.warn('Error toggling sidebar:', error);
            }
        }

        // Toggle sidebar on button click
        sidebarElements.toggle.addEventListener('click', function(event) {
            event.preventDefault();
            event.stopPropagation();
            toggleSidebar();
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            if (window.innerWidth <= 991.98 &&
                !event.target.closest('.sidebar') &&
                !event.target.closest('.sidebar-toggle') &&
                !sidebarElements.sidebar.classList.contains('sidebar-hidden')) {
                toggleSidebar(false);
            }
        });

        // Handle submenu toggles and section visibility
        sidebarElements.allMenuItems.forEach(item => {
            if (!item) return;

            item.addEventListener('click', function(event) {
                try {
                    // Verificar PRIMERO si es un enlace directo antes de preventDefault
                    if (this.tagName.toLowerCase() === 'a') {
                        // Si es un enlace, permitir navegación normal
                        console.log('Enlace directo detectado, permitiendo navegación normal');
                        return; // No interceptar, permitir navegación normal
                    }

                    event.preventDefault();

                    // Handle submenu toggle if applicable
                    if (this.hasAttribute('data-toggle')) {
                        const submenu = this.nextElementSibling;
                        const arrow = this.querySelector('.submenu-arrow');

                        if (submenu?.classList?.contains('submenu')) {
                            submenu.classList.toggle('expanded');
                            this.classList.toggle('active');
                        }
                    }

                    // Handle section visibility with lazy loading integration

                    const sectionId = this.getAttribute('data-section');
                    if (sectionId) {
                        // Integrar con el sistema de lazy loading modular
                        if (window.ModularLoader && typeof window.ModularLoader.loadSection === 'function') {
                            try {
                                // Usar el sistema de lazy loading para cargar la sección
                                window.ModularLoader.loadSection(sectionId);
                                console.log(`Sección ${sectionId} cargada a través del sistema de lazy loading`);
                            } catch (error) {
                                console.warn(`Error cargando sección ${sectionId} con lazy loading:`, error);
                                // Fallback al método anterior si falla el lazy loading
                                fallbackSectionDisplay(sectionId);
                            }
                        } else {
                            // Fallback si el sistema de lazy loading no está disponible
                            console.warn('Sistema de lazy loading no disponible, usando método fallback');
                            fallbackSectionDisplay(sectionId);
                        }
                    }

                    function fallbackSectionDisplay(sectionId) {
                        // Método fallback para mostrar secciones (código original)
                        // Hide all containers
                        document.querySelectorAll('.section-container').forEach(container => {
                            container.style.display = 'none';
                        });

                        // Show the clicked section's container
                        const container = document.getElementById(sectionId + '-container');
                        if (container) {
                            container.style.display = 'block';

                            // Hide hero section and services section when showing a dashboard
                            const heroSection = document.querySelector('.hero-section');
                            const servicesSection = document.querySelector('#services');
                            if (heroSection) heroSection.style.display = 'none';
                            if (servicesSection) servicesSection.style.display = 'none';
                        } else {
                            // Show hero and services sections if no dashboard container found
                            const heroSection = document.querySelector('.hero-section');
                            const servicesSection = document.querySelector('#services');
                            if (heroSection) heroSection.style.display = 'block';
                            if (servicesSection) servicesSection.style.display = 'block';
                        }
                    }
                } catch (error) {
                    console.warn('Error handling menu item click:', error);
                }
            });
        });

        // Handle responsive behavior
        function handleResize() {
            if (window.innerWidth > 991.98) {
                toggleSidebar(true);
            } else {
                toggleSidebar(false);
            }
        }

        window.addEventListener('resize', handleResize);
        handleResize(); // Initialize mobile view

    } catch (error) {
        console.warn('Error initializing sidebar:', error);
    }
}
