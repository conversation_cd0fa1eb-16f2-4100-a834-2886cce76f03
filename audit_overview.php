<?php
/**
 * Sistema de Visualización de Auditorías - Página Principal
 * 
 * Proporciona una interfaz completa para visualizar y gestionar
 * los datos de auditorías en terreno con 146 campos organizados
 * en categorías lógicas para facilitar su interpretación.
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Configuración inicial de la página
if (!defined('PERFORMANCE_MONITORING')) {
    define('PERFORMANCE_MONITORING', false);
}

header('Content-Type: text/html; charset=UTF-8');
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

$start_time = microtime(true);

// Incluir archivos de configuración
require_once 'DatabaseConnection.php';
require_once 'GlobalConfig.php';

// Inicialización
$config = GlobalConfig::getInstance();

try {
    session_start();
    $db = DatabaseConnection::getInstance();
    $conex = $db->getConnection();
    
    register_shutdown_function(function() use ($db) {
        if ($db !== null) {
            $db->cleanup();
        }
    });
    
} catch (Exception $e) {
    error_log("Error de conexión: " . $e->getMessage());
    die("Error de conexión: Por favor, contacte al administrador");
}

// Obtener parámetros de sesión
$sesion = $_GET['id_sesion'] ?? '';

// Validar sesión de usuario
$sql3 = "SELECT tut.nombre, tut.email, tla.RUT, tut.id, tut.PERFIL, tut.PERFIL2,
            tut2.id as id_supervisor, tut.rut AS RUT_OR, tut.nombre_short
        FROM TB_LOG_APP tla
        LEFT JOIN tb_user_tqw tut ON tla.RUT = tut.rut
        LEFT JOIN tb_user_tqw tut2 ON tut2.email = tut.correo_super
        WHERE TOKEN = ? 
        LIMIT 1";

$stmt = $conex->prepare($sql3);
if ($stmt === false) {
    error_log("Error preparing session validation query: " . $conex->error);
    die("Error en la validación de sesión. Por favor, contacte al administrador.");
}

$stmt->bind_param("s", $sesion);

if (!$stmt->execute()) {
    error_log("Error executing session validation query: " . $stmt->error);
    die("Error en la validación de sesión. Por favor, contacte al administrador.");
}

$result = $stmt->get_result();
$row = $result->fetch_assoc();

if (!$row) {
    die("Sesión inválida o expirada");
}

$nombre_user = $row['nombre_short'];
$email = $row['email'];
$perfil = $row['PERFIL'];
$perfil2 = $row['PERFIL2'];
$rut_ejecut = $row['RUT_OR'];

// Obtener parámetros de filtros
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$tecnico = $_GET['tecnico'] ?? '';
$tipo_auditoria = $_GET['tipo_auditoria'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 25;

// Construir condiciones de filtro
$whereConditions = ["1=1"];
$params = [];
$types = "";

if ($date_from && $date_to) {
    $whereConditions[] = "fecha BETWEEN ? AND ?";
    $params[] = $date_from;
    $params[] = $date_to;
    $types .= "ss";
}

if (!empty($tecnico)) {
    $whereConditions[] = "nombre_tecnico = ?";
    $params[] = $tecnico;
    $types .= "s";
}

if (!empty($tipo_auditoria)) {
    $whereConditions[] = "tipo_auditoria = ?";
    $params[] = $tipo_auditoria;
    $types .= "s";
}

$whereClause = implode(' AND ', $whereConditions);

// Obtener datos de resumen
$summaryQuery = "
    SELECT 
        COUNT(*) as total_auditorias,
        COUNT(DISTINCT nombre_tecnico) as total_tecnicos,
        COUNT(DISTINCT supervisor_auditor) as total_supervisores,
        IFNULL(SUM(CASE 
            WHEN insp_estado_acometida = 'CUMPLE' OR insp_estado_conectores = 'CUMPLE' OR 
                 insp_estado_cableado = 'CUMPLE' OR estado_cpe_tx = 'CUMPLE' OR 
                 estado_cpe_rx = 'CUMPLE' OR estado_cpe_snr = 'CUMPLE'
            THEN 1 ELSE 0 
        END), 0) as cumplimientos_totales,
        IFNULL(SUM(CASE 
            WHEN insp_estado_acometida = 'NO CUMPLE' OR insp_estado_conectores = 'NO CUMPLE' OR 
                 insp_estado_cableado = 'NO CUMPLE' OR estado_cpe_tx = 'NO CUMPLE' OR 
                 estado_cpe_rx = 'NO CUMPLE' OR estado_cpe_snr = 'NO CUMPLE'
            THEN 1 ELSE 0 
        END), 0) as incumplimientos_totales
    FROM tb_auditorias_terreno 
    WHERE $whereClause";

$stmt_summary = $conex->prepare($summaryQuery);
if ($stmt_summary === false) {
    error_log("Error preparing summary query: " . $conex->error);
    $summary = [
        'total_auditorias' => 0,
        'total_tecnicos' => 0,
        'total_supervisores' => 0,
        'cumplimientos_totales' => 0,
        'incumplimientos_totales' => 0
    ];
} else {
    if ($params) {
        $stmt_summary->bind_param($types, ...$params);
    }
    if (!$stmt_summary->execute()) {
        error_log("Error executing summary query: " . $stmt_summary->error);
        $summary = [
            'total_auditorias' => 0,
            'total_tecnicos' => 0,
            'total_supervisores' => 0,
            'cumplimientos_totales' => 0,
            'incumplimientos_totales' => 0
        ];
    } else {
        $summary = $stmt_summary->get_result()->fetch_assoc();
    }
}

// Calcular tasa de cumplimiento
$compliance_rate = 0;
if (isset($summary['total_auditorias']) && $summary['total_auditorias'] > 0) {
    $total_checks = (isset($summary['cumplimientos_totales']) ? $summary['cumplimientos_totales'] : 0) + 
                    (isset($summary['incumplimientos_totales']) ? $summary['incumplimientos_totales'] : 0);
    if ($total_checks > 0) {
        $compliance_rate = (isset($summary['cumplimientos_totales']) ? $summary['cumplimientos_totales'] : 0) / $total_checks * 100;
    }
}

// Obtener datos para gráficos - Distribución por tipo
$chartTypesQuery = "
    SELECT tipo_auditoria, COUNT(*) as cantidad
    FROM tb_auditorias_terreno 
    WHERE $whereClause
    GROUP BY tipo_auditoria
    ORDER BY cantidad DESC";

$stmt_chart = $conex->prepare($chartTypesQuery);
if ($stmt_chart === false) {
    error_log("Error preparing chart types query: " . $conex->error);
    $chart_types = [];
} else {
    if ($params) {
        $stmt_chart->bind_param($types, ...$params);
    }
    if (!$stmt_chart->execute()) {
        error_log("Error executing chart types query: " . $stmt_chart->error);
        $chart_types = [];
    } else {
        $chart_types = $stmt_chart->get_result()->fetch_all(MYSQLI_ASSOC);
    }
}

// Obtener datos evolutivos diarios para gráfico
$evolutionQuery = "
    SELECT 
        DATE(fecha) as fecha_dia,
        COUNT(*) as cantidad_auditorias,
        IFNULL(AVG(CASE 
            WHEN insp_estado_acometida = 'CUMPLE' OR insp_estado_conectores = 'CUMPLE' OR 
                 insp_estado_cableado = 'CUMPLE' OR estado_cpe_tx = 'CUMPLE' OR 
                 estado_cpe_rx = 'CUMPLE' OR estado_cpe_snr = 'CUMPLE'
            THEN 1 ELSE 0 
        END) * 100, 0) as promedio_cumplimiento
    FROM tb_auditorias_terreno 
    WHERE $whereClause
    GROUP BY DATE(fecha)
    ORDER BY DATE(fecha) DESC
    LIMIT 30";

$stmt_evolution = $conex->prepare($evolutionQuery);
if ($stmt_evolution === false) {
    error_log("Error preparing evolution query: " . $conex->error);
    $evolution_data = [];
} else {
    if ($params) {
        $stmt_evolution->bind_param($types, ...$params);
    }
    if (!$stmt_evolution->execute()) {
        error_log("Error executing evolution query: " . $stmt_evolution->error);
        $evolution_data = [];
    } else {
        $evolution_result = $stmt_evolution->get_result();
        $evolution_data = array_reverse($evolution_result->fetch_all(MYSQLI_ASSOC));
    }
}

// Obtener datos por supervisor para tabla
$supervisorQuery = "
    SELECT 
        supervisor_auditor,
        COUNT(*) as total_auditorias,
        SUM(CASE 
            WHEN insp_estado_acometida = 'CUMPLE' OR insp_estado_conectores = 'CUMPLE' OR 
                 insp_estado_cableado = 'CUMPLE' OR estado_cpe_tx = 'CUMPLE' OR 
                 estado_cpe_rx = 'CUMPLE' OR estado_cpe_snr = 'CUMPLE'
            THEN 1 ELSE 0 
        END) as auditorias_cumple,
        COUNT(DISTINCT nombre_tecnico) as total_tecnicos,
        IFNULL(AVG(CASE 
            WHEN insp_estado_acometida = 'CUMPLE' OR insp_estado_conectores = 'CUMPLE' OR 
                 insp_estado_cableado = 'CUMPLE' OR estado_cpe_tx = 'CUMPLE' OR 
                 estado_cpe_rx = 'CUMPLE' OR estado_cpe_snr = 'CUMPLE'
            THEN 1 ELSE 0 
        END) * 100, 0) as promedio_cumplimiento
    FROM tb_auditorias_terreno 
    WHERE $whereClause AND supervisor_auditor IS NOT NULL AND supervisor_auditor != ''
    GROUP BY supervisor_auditor
    ORDER BY total_auditorias DESC";

$stmt_supervisor = $conex->prepare($supervisorQuery);
if ($stmt_supervisor === false) {
    error_log("Error preparing supervisor query: " . $conex->error);
    $supervisor_data = [];
} else {
    if ($params) {
        $stmt_supervisor->bind_param($types, ...$params);
    }
    if (!$stmt_supervisor->execute()) {
        error_log("Error executing supervisor query: " . $stmt_supervisor->error);
        $supervisor_data = [];
    } else {
        $supervisor_data = $stmt_supervisor->get_result()->fetch_all(MYSQLI_ASSOC);
    }
}

// Obtener lista de técnicos para filtro
$tecnicosQuery = "SELECT DISTINCT nombre_tecnico FROM tb_auditorias_terreno WHERE nombre_tecnico IS NOT NULL ORDER BY nombre_tecnico";
$tecnicos = $conex->query($tecnicosQuery)->fetch_all(MYSQLI_ASSOC);

// Obtener tipos de auditoría para filtro
$tiposQuery = "SELECT DISTINCT tipo_auditoria FROM tb_auditorias_terreno WHERE tipo_auditoria IS NOT NULL ORDER BY tipo_auditoria";
$tipos_auditoria = $conex->query($tiposQuery)->fetch_all(MYSQLI_ASSOC);

// Obtener datos de auditorías paginados - obtener todos los campos para calcular porcentajes en PHP
$offset = ($page - 1) * $limit;
$auditQuery = "
    SELECT * 
    FROM tb_auditorias_terreno 
    WHERE $whereClause
    ORDER BY fecha DESC, id DESC
    LIMIT $limit OFFSET $offset";

// Depurar la consulta SQL para encontrar el error
$stmt_audit = $conex->prepare($auditQuery);
if ($stmt_audit === false) {
    // Si hay un error en la preparación de la consulta, mostrar el error y terminar
    die("Error en la preparación de la consulta: " . $conex->error . "<br>Consulta: " . $auditQuery);
}

// Si no hay error, continuar con la ejecución normal
if ($params) {
    $stmt_audit->bind_param($types, ...$params);
}
$stmt_audit->execute();
$audits = $stmt_audit->get_result();

// Contar total de registros para paginación
$countQuery = "SELECT COUNT(*) as total FROM tb_auditorias_terreno WHERE $whereClause";
$stmt_count = $conex->prepare($countQuery);
if ($stmt_count === false) {
    // Si hay un error en la preparación de la consulta, mostrar el error y terminar
    die("Error en la preparación de la consulta de conteo: " . $conex->error . "<br>Consulta: " . $countQuery);
}

if ($params) {
    $stmt_count->bind_param($types, ...$params);
}
$stmt_count->execute();
$total_count = $stmt_count->get_result()->fetch_assoc()['total'];
$total_pages = ceil($total_count / $limit);

// Función para calcular porcentajes de cumplimiento usando la misma lógica que los tabs
function calculateCategoryScore($audit, $category) {
    $cumple = 0;
    $total = 0;
    
    switch ($category) {
        case 'tecnica':
            $fields = ['insp_estado_acometida', 'insp_estado_conectores', 'insp_estado_cableado', 
                      'estado_cpe_tx', 'estado_cpe_rx', 'estado_cpe_snr'];
            break;
        case 'epp':
            $fields = [];
            foreach ($audit as $field => $value) {
                if (strpos($field, 'epp_') === 0) {
                    $fields[] = $field;
                }
            }
            break;
        case 'herramientas':
            $fields = [];
            foreach ($audit as $field => $value) {
                if (strpos($field, 'herramientas_') === 0) {
                    $fields[] = $field;
                }
            }
            break;
        case 'vehiculo':
            $fields = [];
            foreach ($audit as $field => $value) {
                if (strpos($field, 'vehiculo_') === 0) {
                    $fields[] = $field;
                }
            }
            break;
        case 'impecabilidad':
            $fields = [];
            foreach ($audit as $field => $value) {
                if (strpos($field, 'impecabilidad_') === 0 || strpos($field, 'presentacion_') === 0) {
                    $fields[] = $field;
                }
            }
            break;
        case 'notebook':
            $fields = [];
            foreach ($audit as $field => $value) {
                if (strpos($field, 'notebook_') === 0 || strpos($field, 'equipo_') === 0) {
                    $fields[] = $field;
                }
            }
            break;
        default:
            return null;
    }
    
    foreach ($fields as $field) {
        if (isset($audit[$field]) && is_string($audit[$field]) && !empty($audit[$field])) {
            $total++;
            if ($audit[$field] === 'CUMPLE') {
                $cumple++;
            }
        }
    }
    
    return $total > 0 ? ($cumple / $total) : null;
}

$end_time = microtime(true);
$execution_time = round(($end_time - $start_time) * 1000, 2);
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Visualización de Auditorías - TELQWAY</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- CSS personalizado -->
    <link rel="stylesheet" href="css/main.css">
    <link rel="stylesheet" href="css/components.css">
    <link rel="stylesheet" href="css/audit_styles.css">
    
    <style>

        html{
            zoom: 82%;
        }
        
        /* Ensure modal overlay is properly sized regardless of zoom */
        .modal-backdrop {
            width: 100vw;
            height: 100vh;
        }
        
        /* Establecer ancho del modal de detalles */
        .modal-xl {
            max-width: 75% !important;
        }

        .content {
            margin-top: 60px;
            padding: 20px;
        }
        
        .audit-card {
            border: 1px solid #dee2e6 !important;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
            transition: transform 0.2s, box-shadow 0.2s;
            background: #ffffff !important;
            background-color: #ffffff !important;
        }
        
        .audit-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }
        
        .metric-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            background: #ffffff !important;
            background-color: #ffffff !important;
        }
        
        .table-container {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
        }
        
        .compliance-badge {
            font-size: 0.875rem;
            padding: 0.375rem 0.75rem;
        }
        
        
        .audit-row:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        
        /* Force white backgrounds for all cards */
        .card,
        .card-body,
        .audit-card .card-body,
        .row .col-lg-3 .card,
        .row .col-lg-8 .card,
        .row .col-md-6 .card {
            background: #ffffff !important;
            background-color: #ffffff !important;
        }
        
        .card-header {
            background: #ffffff !important;
            background-color: #ffffff !important;
            border-bottom: 1px solid #dee2e6 !important;
        }
        
        /* Adjust table font size */
        #auditsTable {
            font-size: 0.875rem !important;
        }
        
        #auditsTable th {
            font-size: 0.9rem !important;
            font-weight: 600;
        }
        
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        
        .loading-spinner {
            text-align: center;
            color: white;
        }
        
        /* Fix tab display issues */
        .tab-content > .tab-pane {
            display: none;
        }
        
        .tab-content > .tab-pane.active {
            display: block;
        }
        
        .tab-content > .tab-pane.show {
            display: block;
        }
    </style>
</head>
<body>
    <?php include('header_supervisor.php'); ?>

    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <div class="spinner-border text-light" role="status">
                <span class="visually-hidden">Cargando...</span>
            </div>
            <div class="mt-3">Procesando datos...</div>
        </div>
    </div>

    <div class="content">
        <!-- Filters Section with Header -->
        <div class="card audit-card mb-4">
            <div class="card-header bg-white border-bottom">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h4 class="mb-1">
                            <i class="bi bi-clipboard-check me-2 text-primary"></i>
                            Visualización de Auditorías
                        </h4>
                        <p class="mb-0 text-muted">
                            Sistema integral para análisis y seguimiento de auditorías en terreno
                        </p>
                    </div>
                    <div class="text-end">
                        <button class="btn btn-primary" id="btnExportData">
                            <i class="bi bi-download me-2"></i>Exportar Datos
                        </button>
                        <button class="btn btn-outline-primary ms-2" id="btnRefreshData">
                            <i class="bi bi-arrow-clockwise me-2"></i>Actualizar
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body bg-light">
                <h5 class="card-title mb-3 text-dark">
                    <i class="bi bi-funnel me-2 text-primary"></i>Filtros de Búsqueda
                </h5>
                <form id="filterForm" class="row g-3">
                    <div class="col-md-3">
                        <label for="date_from" class="form-label text-dark fw-semibold">Fecha desde</label>
                        <input type="date" class="form-control" id="date_from" name="date_from" value="<?php echo $date_from; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="date_to" class="form-label text-dark fw-semibold">Fecha hasta</label>
                        <input type="date" class="form-control" id="date_to" name="date_to" value="<?php echo $date_to; ?>">
                    </div>
                    <div class="col-md-3">
                        <label for="tecnico" class="form-label text-dark fw-semibold">Técnico</label>
                        <select class="form-select" id="tecnico" name="tecnico">
                            <option value="">Todos los técnicos</option>
                            <?php foreach ($tecnicos as $tec): ?>
                                <option value="<?php echo htmlspecialchars($tec['nombre_tecnico']); ?>" 
                                        <?php echo $tecnico == $tec['nombre_tecnico'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($tec['nombre_tecnico']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="tipo_auditoria" class="form-label text-dark fw-semibold">Tipo de Auditoría</label>
                        <select class="form-select" id="tipo_auditoria" name="tipo_auditoria">
                            <option value="">Todos los tipos</option>
                            <?php foreach ($tipos_auditoria as $tipo): ?>
                                <option value="<?php echo htmlspecialchars($tipo['tipo_auditoria']); ?>" 
                                        <?php echo $tipo_auditoria == $tipo['tipo_auditoria'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($tipo['tipo_auditoria']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search me-2"></i>Aplicar Filtros
                        </button>
                        <button type="button" class="btn btn-outline-secondary ms-2" id="clearFilters">
                            <i class="bi bi-x-circle me-2"></i>Limpiar
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card audit-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-primary bg-opacity-10 text-primary me-3">
                                <i class="bi bi-clipboard-data"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-muted mb-1">Total Auditorías</h6>
                                <h3 class="mb-0"><?php echo number_format($summary['total_auditorias']); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card audit-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-success bg-opacity-10 text-success me-3">
                                <i class="bi bi-check-circle"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-muted mb-1">Cumplimiento</h6>
                                <h3 class="mb-0"><?php echo number_format($compliance_rate, 1); ?>%</h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card audit-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-info bg-opacity-10 text-info me-3">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-muted mb-1">Técnicos</h6>
                                <h3 class="mb-0"><?php echo number_format($summary['total_tecnicos']); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card audit-card h-100">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="metric-icon bg-warning bg-opacity-10 text-warning me-3">
                                <i class="bi bi-exclamation-triangle"></i>
                            </div>
                            <div class="flex-grow-1">
                                <h6 class="text-muted mb-1">Incumplimientos</h6>
                                <h3 class="mb-0"><?php echo number_format($summary['incumplimientos_totales']); ?></h3>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="row mb-4">
            <div class="col-lg-8 mb-3">
                <div class="card audit-card">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-bar-chart me-2"></i>Distribución por Tipo de Auditoría
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="auditTypesChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4 mb-3">
                <div class="card audit-card">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pie-chart me-2"></i>Estado General
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="complianceChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Evolution Chart Row -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card audit-card">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-graph-up me-2"></i>Evolución Diaria de Auditorías
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="evolutionChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Supervisor Statistics Table -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card audit-card">
                    <div class="card-header bg-transparent">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-person-check me-2"></i>Auditorías por Supervisor
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0" style="font-size: 0.875rem;">
                                <thead>
                                    <tr>
                                        <th class="text-center">#</th>
                                        <th>Supervisor</th>
                                        <th class="text-center">Total Auditorías</th>
                                        <th class="text-center">Cumplimientos</th>
                                        <th class="text-center">% Cumplimiento</th>
                                        <th class="text-center">Técnicos</th>
                                        <th class="text-center">Rendimiento</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php if (!empty($supervisor_data)): ?>
                                        <?php foreach ($supervisor_data as $index => $supervisor): ?>
                                            <?php 
                                                $cumplimiento_pct = isset($supervisor['promedio_cumplimiento']) ? round($supervisor['promedio_cumplimiento'], 1) : 0;
                                                $badge_class = $cumplimiento_pct >= 90 ? 'success' : ($cumplimiento_pct >= 80 ? 'warning' : ($cumplimiento_pct >= 70 ? 'info' : 'danger'));
                                                $total_tecnicos = isset($supervisor['total_tecnicos']) ? max(1, $supervisor['total_tecnicos']) : 1;
                                                $total_auditorias = isset($supervisor['total_auditorias']) ? $supervisor['total_auditorias'] : 0;
                                                $rendimiento_auditorias_por_tecnico = round($total_auditorias / $total_tecnicos, 1);
                                            ?>
                                            <tr>
                                                <td class="text-center">
                                                    <span class="badge bg-light text-dark"><?php echo $index + 1; ?></span>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="me-3">
                                                            <i class="bi bi-person-badge text-primary fs-5"></i>
                                                        </div>
                                                        <div>
                                                            <strong><?php echo htmlspecialchars($supervisor['supervisor_auditor']); ?></strong>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-primary">
                                                        <?php echo number_format($supervisor['total_auditorias']); ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="text-success fw-semibold">
                                                        <?php echo number_format($supervisor['auditorias_cumple']); ?>
                                                    </span>
                                                    <small class="text-muted">
                                                        / <?php echo number_format($supervisor['total_auditorias']); ?>
                                                    </small>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-<?php echo $badge_class; ?>">
                                                        <?php echo $cumplimiento_pct; ?>%
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <span class="badge bg-info">
                                                        <?php echo number_format($supervisor['total_tecnicos']); ?>
                                                    </span>
                                                </td>
                                                <td class="text-center">
                                                    <small class="text-muted">
                                                        <?php echo $rendimiento_auditorias_por_tecnico; ?>
                                                        <br>aud/téc
                                                    </small>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    <?php else: ?>
                                        <tr>
                                            <td colspan="7" class="text-center py-4">
                                                <div class="text-muted">
                                                    <i class="bi bi-inbox fs-1 d-block mb-3"></i>
                                                    <h6>No hay datos de supervisores</h6>
                                                    <p>No se encontraron supervisores con los filtros aplicados</p>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Data Table -->
        <div class="table-container">
            <div class="card audit-card">
                <div class="card-header bg-transparent d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-table me-2"></i>Listado de Auditorías
                    </h5>
                    <div>
                        <small class="text-muted">
                            Mostrando <?php echo (($page - 1) * $limit) + 1; ?> - 
                            <?php echo min($page * $limit, $total_count); ?> de 
                            <?php echo number_format($total_count); ?> registros
                        </small>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="auditsTable">
                            <thead class="table-light">
                                <tr>
                                    <th>ID</th>
                                    <th>Fecha</th>
                                    <th>Técnico</th>
                                    <th>Tipo</th>
                                    <th>Técnica</th>
                                    <th>EPP</th>
                                    <th>Herramientas</th>
                                    <th>Vehículo</th>
                                    <th>Impecabilidad</th>
                                    <th>Notebook</th>
                                    <th>Acciones</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if ($audits->num_rows > 0): ?>
                                    <?php while ($audit = $audits->fetch_assoc()): ?>
                                        <tr class="audit-row" data-audit-id="<?php echo $audit['id']; ?>">
                                            <td><strong><?php echo $audit['id']; ?></strong></td>
                                            <td><?php echo date('d/m/Y', strtotime($audit['fecha'])); ?></td>
                                            <td><?php echo htmlspecialchars($audit['nombre_tecnico']); ?></td>
                                            <td>
                                                <span class="badge bg-light text-dark">
                                                    <?php echo htmlspecialchars($audit['tipo_auditoria']); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <?php
                                                $score = calculateCategoryScore($audit, 'tecnica');
                                                if ($score === null) {
                                                    echo '<span class="badge rounded-pill bg-secondary">N/A</span>';
                                                } else {
                                                    $percentage = $score * 100;
                                                    $badgeClass = ($percentage >= 80) ? 'bg-success' : (($percentage >= 60) ? 'bg-warning' : 'bg-danger');
                                                    echo '<span class="badge rounded-pill ' . $badgeClass . '">' . number_format($percentage, 0) . '%</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $score = calculateCategoryScore($audit, 'epp');
                                                if ($score === null) {
                                                    echo '<span class="badge rounded-pill bg-secondary">N/A</span>';
                                                } else {
                                                    $percentage = $score * 100;
                                                    $badgeClass = ($percentage >= 80) ? 'bg-success' : (($percentage >= 60) ? 'bg-warning' : 'bg-danger');
                                                    echo '<span class="badge rounded-pill ' . $badgeClass . '">' . number_format($percentage, 0) . '%</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $score = calculateCategoryScore($audit, 'herramientas');
                                                if ($score === null) {
                                                    echo '<span class="badge rounded-pill bg-secondary">N/A</span>';
                                                } else {
                                                    $percentage = $score * 100;
                                                    $badgeClass = ($percentage >= 80) ? 'bg-success' : (($percentage >= 60) ? 'bg-warning' : 'bg-danger');
                                                    echo '<span class="badge rounded-pill ' . $badgeClass . '">' . number_format($percentage, 0) . '%</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $score = calculateCategoryScore($audit, 'vehiculo');
                                                if ($score === null) {
                                                    echo '<span class="badge rounded-pill bg-secondary">N/A</span>';
                                                } else {
                                                    $percentage = $score * 100;
                                                    $badgeClass = ($percentage >= 80) ? 'bg-success' : (($percentage >= 60) ? 'bg-warning' : 'bg-danger');
                                                    echo '<span class="badge rounded-pill ' . $badgeClass . '">' . number_format($percentage, 0) . '%</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $score = calculateCategoryScore($audit, 'impecabilidad');
                                                if ($score === null) {
                                                    echo '<span class="badge rounded-pill bg-secondary">N/A</span>';
                                                } else {
                                                    $percentage = $score * 100;
                                                    $badgeClass = ($percentage >= 80) ? 'bg-success' : (($percentage >= 60) ? 'bg-warning' : 'bg-danger');
                                                    echo '<span class="badge rounded-pill ' . $badgeClass . '">' . number_format($percentage, 0) . '%</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <?php
                                                $score = calculateCategoryScore($audit, 'notebook');
                                                if ($score === null) {
                                                    echo '<span class="badge rounded-pill bg-secondary">N/A</span>';
                                                } else {
                                                    $percentage = $score * 100;
                                                    $badgeClass = ($percentage >= 80) ? 'bg-success' : (($percentage >= 60) ? 'bg-warning' : 'bg-danger');
                                                    echo '<span class="badge rounded-pill ' . $badgeClass . '">' . number_format($percentage, 0) . '%</span>';
                                                }
                                                ?>
                                            </td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary view-details" 
                                                        data-audit-id="<?php echo $audit['id']; ?>">
                                                    <i class="bi bi-eye"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endwhile; ?>
                                <?php else: ?>
                                    <tr>
                                        <td colspan="11" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="bi bi-inbox display-4 d-block mb-3"></i>
                                                No se encontraron auditorías con los filtros aplicados.
                                            </div>
                                        </td>
                                    </tr>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Pagination -->
                <?php if ($total_pages > 1): ?>
                <div class="card-footer bg-transparent">
                    <nav aria-label="Paginación de auditorías">
                        <ul class="pagination justify-content-center mb-0">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo buildPaginationUrl($page - 1, $date_from, $date_to, $tecnico, $tipo_auditoria); ?>">
                                        <i class="bi bi-chevron-left"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                            
                            <?php
                            $start_page = max(1, $page - 2);
                            $end_page = min($total_pages, $page + 2);
                            
                            for ($i = $start_page; $i <= $end_page; $i++): ?>
                                <li class="page-item <?php echo ($i == $page) ? 'active' : ''; ?>">
                                    <a class="page-link" href="<?php echo buildPaginationUrl($i, $date_from, $date_to, $tecnico, $tipo_auditoria); ?>">
                                        <?php echo $i; ?>
                                    </a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $total_pages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="<?php echo buildPaginationUrl($page + 1, $date_from, $date_to, $tecnico, $tipo_auditoria); ?>">
                                        <i class="bi bi-chevron-right"></i>
                                    </a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
                <?php endif; ?>
            </div>
        </div>

        
    </div>

    <!-- Audit Detail Modal -->
    <div class="modal fade" id="auditDetailModal" tabindex="-1" aria-labelledby="auditDetailModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl modal-dialog-scrollable">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="auditDetailModalLabel">
                        <i class="bi bi-clipboard-check me-2"></i>Detalle de Auditoría
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body" id="auditDetailContent">
                    <div class="text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-3">Cargando detalles de auditoría...</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <span class="me-auto small text-muted">
                        <i class="bi bi-info-circle"></i> Use las pestañas para navegar entre las secciones
                    </span>
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x-circle me-1"></i>Cerrar
                    </button>
                    <button type="button" class="btn btn-primary" id="btnPrintAudit">
                        <i class="bi bi-printer me-1"></i>Imprimir Detalle
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Libraries -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Configuration and data
        const chartData = {
            types: <?php echo json_encode($chart_types); ?>,
            compliance: {
                cumple: <?php echo $summary['cumplimientos_totales']; ?>,
                no_cumple: <?php echo $summary['incumplimientos_totales']; ?>
            },
            evolution: <?php echo json_encode($evolution_data); ?>
        };
        
        const sessionId = '<?php echo $sesion; ?>';
        
        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            setupEventHandlers();
        });
        
        // Initialize charts
        function initializeCharts() {
            // Audit Types Chart
            const typesCtx = document.getElementById('auditTypesChart').getContext('2d');
            new Chart(typesCtx, {
                type: 'pie',
                data: {
                    labels: chartData.types.map(item => item.tipo_auditoria),
                    datasets: [{
                        label: 'Cantidad de Auditorías',
                        data: chartData.types.map(item => item.cantidad),
                        backgroundColor: [
                            'rgba(54, 162, 235, 0.8)',
                            'rgba(255, 99, 132, 0.8)',
                            'rgba(255, 205, 86, 0.8)',
                            'rgba(75, 192, 192, 0.8)',
                            'rgba(153, 102, 255, 0.8)',
                            'rgba(255, 159, 64, 0.8)',
                            'rgba(199, 199, 199, 0.8)',
                            'rgba(83, 102, 255, 0.8)'
                        ],
                        borderColor: [
                            'rgba(54, 162, 235, 1)',
                            'rgba(255, 99, 132, 1)',
                            'rgba(255, 205, 86, 1)',
                            'rgba(75, 192, 192, 1)',
                            'rgba(153, 102, 255, 1)',
                            'rgba(255, 159, 64, 1)',
                            'rgba(199, 199, 199, 1)',
                            'rgba(83, 102, 255, 1)'
                        ],
                        borderWidth: 2,
                        hoverOffset: 10
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: true,
                            position: 'right',
                            labels: {
                                usePointStyle: true,
                                padding: 15,
                                font: {
                                    size: 12
                                },
                                generateLabels: function(chart) {
                                    const data = chart.data;
                                    if (data.labels.length && data.datasets.length) {
                                        return data.labels.map((label, i) => {
                                            const value = data.datasets[0].data[i];
                                            const total = data.datasets[0].data.reduce((a, b) => a + b, 0);
                                            const percentage = ((value / total) * 100).toFixed(1);
                                            return {
                                                text: `${label} (${percentage}%)`,
                                                fillStyle: data.datasets[0].backgroundColor[i],
                                                strokeStyle: data.datasets[0].borderColor[i],
                                                lineWidth: data.datasets[0].borderWidth,
                                                hidden: false,
                                                index: i
                                            };
                                        });
                                    }
                                    return [];
                                }
                            }
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.parsed;
                                    const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                    const percentage = ((value / total) * 100).toFixed(1);
                                    return `${label}: ${value} auditorías (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
            
            // Compliance Chart
            const complianceCtx = document.getElementById('complianceChart').getContext('2d');
            new Chart(complianceCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Cumple', 'No Cumple'],
                    datasets: [{
                        data: [chartData.compliance.cumple, chartData.compliance.no_cumple],
                        backgroundColor: [
                            'rgba(40, 167, 69, 0.8)',
                            'rgba(220, 53, 69, 0.8)'
                        ],
                        borderColor: [
                            'rgba(40, 167, 69, 1)',
                            'rgba(220, 53, 69, 1)'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
            
            // Evolution Chart
            const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
            new Chart(evolutionCtx, {
                type: 'line',
                data: {
                    labels: chartData.evolution.map(item => {
                        const date = new Date(item.fecha_dia);
                        return date.toLocaleDateString('es-ES', { 
                            day: '2-digit', 
                            month: '2-digit' 
                        });
                    }),
                    datasets: [
                        {
                            label: 'Cantidad de Auditorías',
                            data: chartData.evolution.map(item => item.cantidad_auditorias),
                            borderColor: 'rgba(54, 162, 235, 1)',
                            backgroundColor: 'rgba(54, 162, 235, 0.1)',
                            borderWidth: 3,
                            fill: true,
                            tension: 0.3,
                            pointBackgroundColor: 'rgba(54, 162, 235, 1)',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            yAxisID: 'y'
                        },
                        {
                            label: 'Promedio Cumplimiento (%)',
                            data: chartData.evolution.map(item => parseFloat(item.promedio_cumplimiento)),
                            borderColor: 'rgba(40, 167, 69, 1)',
                            backgroundColor: 'rgba(40, 167, 69, 0.1)',
                            borderWidth: 3,
                            fill: false,
                            tension: 0.3,
                            pointBackgroundColor: 'rgba(40, 167, 69, 1)',
                            pointBorderColor: '#ffffff',
                            pointBorderWidth: 2,
                            pointRadius: 6,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'top',
                            labels: {
                                usePointStyle: true,
                                padding: 20
                            }
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                title: function(context) {
                                    const index = context[0].dataIndex;
                                    const date = new Date(chartData.evolution[index].fecha_dia);
                                    return date.toLocaleDateString('es-ES', { 
                                        weekday: 'long',
                                        day: '2-digit', 
                                        month: 'long',
                                        year: 'numeric'
                                    });
                                }
                            }
                        }
                    },
                    interaction: {
                        mode: 'nearest',
                        axis: 'x',
                        intersect: false
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: 'Fecha'
                            },
                            grid: {
                                color: 'rgba(0, 0, 0, 0.1)'
                            }
                        },
                        y: {
                            type: 'linear',
                            display: true,
                            position: 'left',
                            title: {
                                display: true,
                                text: 'Cantidad de Auditorías'
                            },
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1
                            },
                            grid: {
                                color: 'rgba(54, 162, 235, 0.1)'
                            }
                        },
                        y1: {
                            type: 'linear',
                            display: true,
                            position: 'right',
                            title: {
                                display: true,
                                text: 'Promedio Cumplimiento (%)'
                            },
                            beginAtZero: true,
                            max: 100,
                            ticks: {
                                callback: function(value) {
                                    return value + '%';
                                }
                            },
                            grid: {
                                drawOnChartArea: false
                            }
                        }
                    }
                }
            });
        }
        
        // Setup event handlers
        function setupEventHandlers() {
            // Filter form submission
            document.getElementById('filterForm').addEventListener('submit', function(e) {
                e.preventDefault();
                applyFilters();
            });
            
            // Clear filters
            document.getElementById('clearFilters').addEventListener('click', function() {
                clearFilters();
            });
            
            // View details buttons
            document.querySelectorAll('.view-details').forEach(button => {
                button.addEventListener('click', function() {
                    const auditId = this.dataset.auditId;
                    openAuditDetailModal(auditId);
                });
            });
            
            // Export data
            document.getElementById('btnExportData').addEventListener('click', function() {
                exportAuditData();
            });
            
            // Refresh data
            document.getElementById('btnRefreshData').addEventListener('click', function() {
                window.location.reload();
            });
        }
        
        // Apply filters
        function applyFilters() {
            showLoading();
            
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);
            params.append('id_sesion', sessionId);
            
            window.location.href = '?' + params.toString();
        }
        
        // Clear filters
        function clearFilters() {
            document.getElementById('date_from').value = '';
            document.getElementById('date_to').value = '';
            document.getElementById('tecnico').value = '';
            document.getElementById('tipo_auditoria').value = '';
            
            window.location.href = '?id_sesion=' + sessionId;
        }
        
        // Show loading overlay
        function showLoading() {
            document.getElementById('loadingOverlay').style.display = 'flex';
        }
        
        // Hide loading overlay
        function hideLoading() {
            document.getElementById('loadingOverlay').style.display = 'none';
        }
        
        // Open audit detail modal
        function openAuditDetailModal(auditId) {
            const modal = new bootstrap.Modal(document.getElementById('auditDetailModal'));
            modal.show();
            
            // Load audit details via AJAX
            fetch(`get_audit_data.php?type=detail&id=${auditId}&id_sesion=${sessionId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        renderAuditDetail(data.data);
                    } else {
                        showError('Error al cargar los detalles: ' + data.message);
                    }
                })
                .catch(error => {
                    showError('Error de conexión: ' + error.message);
                });
        }
        
        // Render audit detail
        function renderAuditDetail(responseData) {
            const content = document.getElementById('auditDetailContent');
            const { audit, categorized, compliance_stats } = responseData;
            
            // Update modal title with audit info
            document.getElementById('auditDetailModalLabel').innerHTML = `
                <i class="bi bi-clipboard-check me-2"></i>
                Auditoría #${audit.id} - ${formatDate(audit.fecha)}
            `;
            
            // Ya no mostramos el resumen de cumplimiento general en la parte superior
            // Solo mostramos los resúmenes dentro de cada tab
            let summaryHtml = '';
            
            // Create tabs using the categorized data from the API
            let tabsHtml = '<ul class="nav nav-pills nav-fill gap-1 mb-3" id="auditDetailTabs" role="tablist" style="border-bottom: 1px solid #eee; padding-bottom: 8px;">';
            let contentHtml = '<div class="tab-content mt-3" id="auditDetailTabContent">';
            
            const categoryInfo = {
                'basica': { title: 'Información Básica', icon: 'bi-info-circle' },
                'tecnica': { title: 'Inspección Técnica', icon: 'bi-tools' },
                'herramientas': { title: 'Herramientas', icon: 'bi-wrench' },
                'epp': { title: 'Equipos de Protección', icon: 'bi-shield-check' },
                'vehiculo': { title: 'Vehículo', icon: 'bi-truck' },
                'mediciones': { title: 'Mediciones', icon: 'bi-graph-up' },
                'impecabilidad': { title: 'Impecabilidad', icon: 'bi-check2-all' },
                'notebook': { title: 'Notebook', icon: 'bi-laptop' }
            };
            
            let tabIndex = 0;
            Object.entries(categoryInfo).forEach(([key, info]) => {
                if (categorized[key] && Object.keys(categorized[key]).length > 0) {
                    const active = tabIndex === 0 ? 'active' : '';
                    
                    // Calcular estadísticas de cumplimiento para mostrar en las pestañas
                    let tabSummary = '';
                    let cumple = 0;
                    let noCumple = 0;
                    let noAplica = 0;
                    let total = 0;
                    
                    Object.entries(categorized[key]).forEach(([field, value]) => {
                        if (typeof value === 'string') {
                            total++;
                            if (value === 'CUMPLE') {
                                cumple++;
                            } else if (value === 'NO CUMPLE') {
                                noCumple++;
                            } else if (value === 'No Aplica') {
                                noAplica++;
                            }
                        }
                    });
                    
                    const percentage = total > 0 ? Math.round((cumple / total) * 100) : 0;
                    const badgeColor = percentage >= 80 ? 'success' : percentage >= 60 ? 'warning' : 'danger';
                    
                    // Mostrar porcentajes solo en las pestañas si hay datos de cumplimiento
                    // Excluir el tab de información básica ya que no tiene campos de cumplimiento
                    if (total > 0 && key !== 'basica') {
                        const badgeIcon = percentage >= 80 ? 'bi-check-circle-fill' : 
                                         percentage >= 60 ? 'bi-exclamation-circle-fill' : 'bi-x-circle-fill';
                        tabSummary = `
                            <span class="badge bg-${badgeColor} ms-2 d-flex align-items-center">
                                <i class="bi ${badgeIcon} me-1"></i>${percentage}%
                            </span>`;
                    }
                    
                    tabsHtml += `
                        <li class="nav-item" role="presentation">
                            <button class="nav-link ${active} py-2 px-2 rounded-pill d-flex align-items-center justify-content-center" 
                                   id="${key}-tab" data-bs-toggle="tab" 
                                   data-bs-target="#${key}" type="button" role="tab" style="font-size: 0.9rem;">
                                <div>
                                    <i class="${info.icon} me-1"></i><span>${info.title}</span>
                                    ${tabSummary}
                                </div>
                            </button>
                        </li>
                    `;
                    
                    contentHtml += `
                        <div class="tab-pane fade ${active ? 'show active' : ''}" id="${key}" role="tabpanel" aria-labelledby="${key}-tab">
                    `;
                    
                    // Diseño unificado de tabla para todos los tabs
                    contentHtml += '<div class="table-responsive"><table class="table table-sm table-hover">';
                    contentHtml += `<thead class="table-light">
                        <tr>
                            <th>Ítem</th>
                            <th class="text-center">Estado</th>
                        </tr>
                    </thead><tbody>`;
                    
                    // Mostrar filas de la tabla
                    Object.entries(categorized[key]).forEach(([field, value]) => {
                        if (value !== null && value !== '') {
                            const label = getFieldDisplayName(field);
                            const formattedValue = formatFieldValue(field, value);
                            
                            // Definir color para el estado
                            let rowClass = '';
                            
                            if (value === 'CUMPLE') {
                                rowClass = 'table-success';
                            } else if (value === 'NO CUMPLE') {
                                rowClass = 'table-danger';
                            } else if (value === 'No Aplica') {
                                rowClass = 'table-secondary';
                            }
                            
                            contentHtml += `
                                <tr class="${rowClass}">
                                    <td class="fw-semibold">${label}</td>
                                    <td class="text-center">${formattedValue}</td>
                                </tr>
                            `;
                        }
                    });
                    
                    contentHtml += '</tbody></table></div>';
                    
                    contentHtml += '</div>';
                    tabIndex++;
                }
            });
            
            if (tabIndex === 0) {
                contentHtml = '<div class="alert alert-info">No hay datos disponibles para mostrar.</div>';
                tabsHtml = '';
                content.innerHTML = summaryHtml + '<div class="alert alert-info mt-3">No hay datos detallados disponibles para mostrar.</div>';
            } else {
                tabsHtml += '</ul>';
                contentHtml += '</div>';
                content.innerHTML = summaryHtml + tabsHtml + contentHtml;
            }
        }
        
        
        // Get display name for field
        function getFieldDisplayName(field) {
            const fieldNames = {
                'id': 'ID',
                'fecha': 'Fecha',
                'supervisor_auditor': 'Supervisor',
                'nombre_tecnico': 'Técnico',
                'tipo_auditoria': 'Tipo de Auditoría',
                'rut': 'RUT',
                'numero_orden': 'Número de Orden',
                'direccion': 'Dirección',
                'comuna': 'Comuna',
                'tipo_actividad': 'Tipo de Actividad',
                'tipo_servicio': 'Tipo de Servicio',
                'insp_estado_acometida': 'Estado Acometida',
                'insp_estado_conectores': 'Estado Conectores',
                'insp_estado_cableado': 'Estado Cableado',
                'estado_cpe_tx': 'Estado CPE TX',
                'estado_cpe_rx': 'Estado CPE RX',
                'estado_cpe_snr': 'Estado CPE SNR'
            };
            
            return fieldNames[field] || field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
        }
        
        // Format field value for display
        function formatFieldValue(field, value) {
            if (value === null || value === undefined || value === '') {
                return '<span class="text-muted">No especificado</span>';
            }
            
            // Format specific field types
            if (field === 'fecha') {
                return formatDate(value);
            }
            
            if (['insp_estado_acometida', 'insp_estado_conectores', 'insp_estado_cableado', 
                 'estado_cpe_tx', 'estado_cpe_rx', 'estado_cpe_snr'].includes(field)) {
                return getStatusBadge(value);
            }
            
            if (typeof value === 'string' && value.startsWith('https://drive.google.com')) {
                return `<a href="${value}" target="_blank" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-image"></i> Ver Imagen
                </a>`;
            }
            
            return escapeHtml(value.toString());
        }
        
        // Get status badge HTML
        function getStatusBadge(status) {
            switch (status) {
                case 'CUMPLE':
                    return '<div class="d-inline-flex align-items-center"><span class="badge bg-success d-flex align-items-center justify-content-center" style="width: 28px; height: 28px; border-radius: 50%;"><i class="bi bi-check-lg"></i></span><span class="ms-2 text-success fw-bold">Cumple</span></div>';
                case 'NO CUMPLE':
                    return '<div class="d-inline-flex align-items-center"><span class="badge bg-danger d-flex align-items-center justify-content-center" style="width: 28px; height: 28px; border-radius: 50%;"><i class="bi bi-x-lg"></i></span><span class="ms-2 text-danger fw-bold">No Cumple</span></div>';
                case 'No Aplica':
                    return '<div class="d-inline-flex align-items-center"><span class="badge bg-secondary d-flex align-items-center justify-content-center" style="width: 28px; height: 28px; border-radius: 50%;"><i class="bi bi-dash-lg"></i></span><span class="ms-2 text-secondary fw-bold">No Aplica</span></div>';
                default:
                    return escapeHtml(status);
            }
        }
        
        // Format date for display
        function formatDate(dateString) {
            if (!dateString) return '';
            
            const date = new Date(dateString);
            return date.toLocaleDateString('es-ES', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            });
        }
        
        // Escape HTML to prevent XSS
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
        
        // Show error message
        function showError(message) {
            document.getElementById('auditDetailContent').innerHTML = `
                <div class="alert alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    ${message}
                </div>
            `;
        }
        
        // Export audit data
        function exportAuditData() {
            const form = document.getElementById('filterForm');
            const formData = new FormData(form);
            const params = new URLSearchParams(formData);
            params.append('id_sesion', sessionId);
            params.append('export', '1');
            
            window.open('export_audit_data.php?' + params.toString(), '_blank');
        }
        
        // Print audit
        document.getElementById('btnPrintAudit').addEventListener('click', function() {
            window.print();
        });
    </script>
</body>
</html>

<?php
// Helper function for pagination URLs
function buildPaginationUrl($page, $date_from, $date_to, $tecnico, $tipo_auditoria) {
    $params = [
        'id_sesion' => $_GET['id_sesion'] ?? '',
        'page' => $page
    ];
    
    if ($date_from) $params['date_from'] = $date_from;
    if ($date_to) $params['date_to'] = $date_to;
    if ($tecnico) $params['tecnico'] = $tecnico;
    if ($tipo_auditoria) $params['tipo_auditoria'] = $tipo_auditoria;
    
    return '?' . http_build_query($params);
}
?>