/**
 * Sistema Modular de Lazy Loading para TQW v3.0
 * 
 * Arquitectura profesional con módulos JavaScript independientes por sección
 * Características:
 * - Módulos JS completamente independientes por sección
 * - Gestión de dependencias
 * - Sistema de eventos robusto
 * - Caché inteligente
 * - Manejo de errores mejorado
 */

(function() {
    'use strict';

    class ModularLazyLoader {
        constructor() {
            // Configuración del sistema
            this.config = {
                debug: window.location.hash === '#debug',
                cacheEnabled: true,
                retryAttempts: 3,
                retryDelay: 1000,
                loadingTimeout: 30000
            };

            // Estado del sistema
            this.state = {
                loadedSections: new Set(),
                loadingSections: new Set(),
                sectionModules: new Map(),
                sectionCache: new Map(),
                activeSection: null
            };

            // Mapeo de secciones a archivos
            this.sectionMapping = {
                'analytics': 'get_analytics_section.php',
                'dash_logis': 'get_dash_logis.php',
                'desafio_tec_section': 'get_desafio_tec_section.php',
                'cubos': 'get_cubos.php',
                'calidad_reac_section': 'get_calidad_reac_section.php',
                'inventario': 'get_inventario.php',
                'Home': 'get_Home.php',
                'reporte_produccion': 'get_reporte_produccion.php',
                'solicitudMaterial': 'get_solicitudes.php',
                'nicolas_personal': 'get_nicolas_personal.php',
                'facturacion': 'modulo_factura.php',
                'audit_overview': 'get_audit_overview.php'
            };

            // Registro de módulos JS por sección
            this.moduleRegistry = new Map();
            
            // Event bus para comunicación entre módulos
            this.eventBus = new EventTarget();
        }

        /**
         * Inicializa el sistema
         */
        init() {
            this.log('🚀 Inicializando Sistema Modular de Lazy Loading v3.0');
            
            // Registrar módulos de secciones
            this.registerSectionModules();
            
            // Configurar navegación
            this.setupNavigation();
            
            // Configurar observadores
            this.setupObservers();
            
            // Cargar sección inicial si existe
            this.loadInitialSection();
            
            // Exponer API global
            window.ModularLoader = this;
            
            this.log('✅ Sistema inicializado correctamente');
        }

        /**
         * Registra los módulos JS de cada sección
         */
        registerSectionModules() {
            // Analytics Module
            this.registerModule('analytics', {
                name: 'AnalyticsModule',
                dependencies: [],
                init: function(container, loader) {
                    // Módulo específico para Analytics
                    const module = {
                        container: container,
                        loader: loader,
                        
                        initialize() {
                            this.setupTable();
                            this.setupExport();
                            this.bindEvents();
                        },
                        
                        setupTable() {
                            const table = container.querySelector('#tablaAnalytics');
                            if (!table) return;
                            
                            // Configurar ordenamiento
                            this.initSorting(table);
                            // Configurar filtros
                            this.initFilters(table);
                        },
                        
                        setupExport() {
                            const downloadBtn = container.querySelector('#downloadTable_analytics');
                            if (downloadBtn) {
                                downloadBtn.addEventListener('click', (e) => {
                                    e.preventDefault();
                                    this.exportTable();
                                });
                            }
                        },
                        
                        exportTable() {
                            const table = container.querySelector('#tablaAnalytics');
                            if (!table) return;
                            
                            // Lógica de exportación
                            this.exportToExcel(table, 'Analytics_' + new Date().toISOString().slice(0, 10));
                        },
                        
                        exportToExcel(table, filename) {
                            // Implementación de exportación a Excel
                            const wb = XLSX.utils.table_to_book(table);
                            XLSX.writeFile(wb, filename + '.xlsx');
                        },
                        
                        initSorting(table) {
                            const headers = table.querySelectorAll('th[data-sortable]');
                            headers.forEach(header => {
                                header.style.cursor = 'pointer';
                                header.addEventListener('click', () => this.sortTable(table, header));
                            });
                        },
                        
                        sortTable(table, header) {
                            const column = header.cellIndex;
                            const tbody = table.querySelector('tbody');
                            const rows = Array.from(tbody.querySelectorAll('tr'));
                            const isAscending = header.classList.contains('asc');
                            
                            rows.sort((a, b) => {
                                const aValue = a.cells[column].textContent.trim();
                                const bValue = b.cells[column].textContent.trim();
                                
                                if (!isNaN(aValue) && !isNaN(bValue)) {
                                    return isAscending ? 
                                        parseFloat(aValue) - parseFloat(bValue) : 
                                        parseFloat(bValue) - parseFloat(aValue);
                                }
                                
                                return isAscending ? 
                                    aValue.localeCompare(bValue) : 
                                    bValue.localeCompare(aValue);
                            });
                            
                            tbody.innerHTML = '';
                            rows.forEach(row => tbody.appendChild(row));
                            
                            // Actualizar estado visual
                            table.querySelectorAll('th').forEach(th => th.classList.remove('asc', 'desc'));
                            header.classList.toggle('asc', !isAscending);
                            header.classList.toggle('desc', isAscending);
                        },
                        
                        initFilters(table) {
                            const filters = container.querySelectorAll('.column-search');
                            filters.forEach(filter => {
                                filter.addEventListener('input', () => this.filterTable(table));
                            });
                        },
                        
                        filterTable(table) {
                            const filters = container.querySelectorAll('.column-search');
                            const tbody = table.querySelector('tbody');
                            const rows = tbody.querySelectorAll('tr');
                            
                            rows.forEach(row => {
                                let visible = true;
                                
                                filters.forEach(filter => {
                                    const column = parseInt(filter.getAttribute('data-column'));
                                    const value = filter.value.toLowerCase();
                                    const cellValue = row.cells[column]?.textContent.toLowerCase() || '';
                                    
                                    if (value && !cellValue.includes(value)) {
                                        visible = false;
                                    }
                                });
                                
                                row.style.display = visible ? '' : 'none';
                            });
                        },
                        
                        bindEvents() {
                            // Eventos específicos del módulo
                            loader.eventBus.addEventListener('analytics:refresh', () => {
                                this.refresh();
                            });
                        },
                        
                        refresh() {
                            loader.reloadSection('analytics');
                        },
                        
                        destroy() {
                            // Limpieza al descargar el módulo
                            this.unbindEvents();
                        },
                        
                        unbindEvents() {
                            // Remover event listeners
                        }
                    };
                    
                    module.initialize();
                    return module;
                }
            });

            // Nicolas Personal Module
            this.registerModule('nicolas_personal', {
                name: 'NicolasPersonalModule',
                dependencies: [],
                init: function(container, loader) {
                    const module = {
                        container: container,
                        loader: loader,
                        filters: {
                            rut: '',
                            fecha: '',
                            actividad: ''
                        },
                        
                        initialize() {
                            this.setupFilters();
                            this.setupTable();
                            this.bindEvents();
                            this.loadInitialData();
                        },
                        
                        setupFilters() {
                            // Configurar filtro RUT
                            const rutFilter = container.querySelector('#filtroRut');
                            if (rutFilter) {
                                rutFilter.addEventListener('input', (e) => {
                                    this.filters.rut = e.target.value;
                                    this.applyFilters();
                                });
                            }
                            
                            // Configurar filtro fecha
                            const fechaFilter = container.querySelector('#filtroFecha');
                            if (fechaFilter) {
                                fechaFilter.addEventListener('change', (e) => {
                                    this.filters.fecha = e.target.value;
                                    this.applyFilters();
                                });
                            }
                            
                            // Configurar filtro actividad
                            const actividadFilter = container.querySelector('#filtroActividad');
                            if (actividadFilter) {
                                actividadFilter.addEventListener('change', (e) => {
                                    this.filters.actividad = e.target.value;
                                    this.applyFilters();
                                });
                            }
                        },
                        
                        setupTable() {
                            const table = container.querySelector('#tablaTQW');
                            if (!table) return;
                            
                            // Configurar ordenamiento
                            const headers = table.querySelectorAll('th[data-sort]');
                            headers.forEach(header => {
                                header.style.cursor = 'pointer';
                                header.addEventListener('click', () => {
                                    this.sortTable(header.getAttribute('data-sort'));
                                });
                            });
                        },
                        
                        applyFilters() {
                            const tbody = container.querySelector('#tablaTQW tbody');
                            if (!tbody) return;
                            
                            const rows = tbody.querySelectorAll('tr');
                            
                            rows.forEach(row => {
                                let visible = true;
                                
                                // Filtrar por RUT
                                if (this.filters.rut) {
                                    const rutCell = row.querySelector('[data-rut]');
                                    if (rutCell && !rutCell.textContent.includes(this.filters.rut)) {
                                        visible = false;
                                    }
                                }
                                
                                // Filtrar por fecha
                                if (this.filters.fecha) {
                                    const fechaCell = row.querySelector('[data-fecha]');
                                    if (fechaCell && !fechaCell.textContent.includes(this.filters.fecha)) {
                                        visible = false;
                                    }
                                }
                                
                                // Filtrar por actividad
                                if (this.filters.actividad) {
                                    const actividadCell = row.querySelector('[data-actividad]');
                                    if (actividadCell && actividadCell.textContent !== this.filters.actividad) {
                                        visible = false;
                                    }
                                }
                                
                                row.style.display = visible ? '' : 'none';
                            });
                            
                            this.updateResultCount();
                        },
                        
                        updateResultCount() {
                            const tbody = container.querySelector('#tablaTQW tbody');
                            const visibleRows = tbody.querySelectorAll('tr:not([style*="display: none"])');
                            const countElement = container.querySelector('#resultCount');
                            
                            if (countElement) {
                                countElement.textContent = `Mostrando ${visibleRows.length} registros`;
                            }
                        },
                        
                        sortTable(column) {
                            const tbody = container.querySelector('#tablaTQW tbody');
                            const rows = Array.from(tbody.querySelectorAll('tr'));
                            const header = container.querySelector(`th[data-sort="${column}"]`);
                            const isAscending = header.classList.contains('sort-asc');
                            
                            rows.sort((a, b) => {
                                const aValue = a.querySelector(`[data-${column}]`)?.textContent || '';
                                const bValue = b.querySelector(`[data-${column}]`)?.textContent || '';
                                
                                return isAscending ? 
                                    bValue.localeCompare(aValue) : 
                                    aValue.localeCompare(bValue);
                            });
                            
                            tbody.innerHTML = '';
                            rows.forEach(row => tbody.appendChild(row));
                            
                            // Actualizar indicadores visuales
                            container.querySelectorAll('th[data-sort]').forEach(th => {
                                th.classList.remove('sort-asc', 'sort-desc');
                            });
                            
                            header.classList.toggle('sort-asc', !isAscending);
                            header.classList.toggle('sort-desc', isAscending);
                        },
                        
                        loadInitialData() {
                            // Aplicar filtros iniciales si hay valores por defecto
                            this.applyFilters();
                        },
                        
                        bindEvents() {
                            // Botón de actualización
                            const refreshBtn = container.querySelector('#btnRefresh');
                            if (refreshBtn) {
                                refreshBtn.addEventListener('click', () => {
                                    loader.reloadSection('nicolas_personal');
                                });
                            }
                            
                            // Botón de exportar
                            const exportBtn = container.querySelector('#btnExport');
                            if (exportBtn) {
                                exportBtn.addEventListener('click', () => {
                                    this.exportData();
                                });
                            }
                        },
                        
                        exportData() {
                            // Implementar exportación
                            console.log('Exportando datos filtrados...');
                        },
                        
                        destroy() {
                            // Limpieza del módulo
                        }
                    };
                    
                    module.initialize();
                    return module;
                }
            });

            // Dashboard Logística Module
            this.registerModule('dash_logis', {
                name: 'DashboardLogisticaModule',
                dependencies: [],
                init: function(container, loader) {
                    const module = {
                        container: container,
                        loader: loader,
                        refreshInterval: null,
                        
                        initialize() {
                            this.setupDashboard();
                            this.setupAutoRefresh();
                            this.bindEvents();
                        },
                        
                        setupDashboard() {
                            // Inicializar gráficos si existen
                            this.initCharts();
                            
                            // Configurar tabla principal
                            this.setupTable();
                            
                            // Configurar indicadores
                            this.updateIndicators();
                        },
                        
                        initCharts() {
                            const chartContainers = container.querySelectorAll('[data-chart]');
                            
                            chartContainers.forEach(chartContainer => {
                                const chartType = chartContainer.getAttribute('data-chart');
                                const chartData = JSON.parse(chartContainer.getAttribute('data-chart-data') || '{}');
                                
                                this.renderChart(chartContainer, chartType, chartData);
                            });
                        },
                        
                        renderChart(container, type, data) {
                            if (typeof ApexCharts !== 'undefined') {
                                const options = {
                                    chart: {
                                        type: type,
                                        height: 350
                                    },
                                    series: data.series || [],
                                    xaxis: data.categories ? { categories: data.categories } : {}
                                };
                                
                                const chart = new ApexCharts(container, options);
                                chart.render();
                                
                                // Guardar referencia para limpieza
                                container._chart = chart;
                            }
                        },
                        
                        setupTable() {
                            const table = container.querySelector('#tablaLogistica');
                            if (!table) return;
                            
                            // Agregar funcionalidad de búsqueda
                            const searchInput = container.querySelector('#searchLogistica');
                            if (searchInput) {
                                searchInput.addEventListener('input', (e) => {
                                    this.searchTable(e.target.value);
                                });
                            }
                        },
                        
                        searchTable(searchTerm) {
                            const table = container.querySelector('#tablaLogistica');
                            const tbody = table.querySelector('tbody');
                            const rows = tbody.querySelectorAll('tr');
                            
                            const term = searchTerm.toLowerCase();
                            
                            rows.forEach(row => {
                                const text = row.textContent.toLowerCase();
                                row.style.display = text.includes(term) ? '' : 'none';
                            });
                        },
                        
                        updateIndicators() {
                            // Actualizar KPIs
                            const indicators = container.querySelectorAll('[data-indicator]');
                            
                            indicators.forEach(indicator => {
                                const type = indicator.getAttribute('data-indicator');
                                const value = this.calculateIndicator(type);
                                indicator.textContent = value;
                            });
                        },
                        
                        calculateIndicator(type) {
                            // Lógica para calcular indicadores
                            switch(type) {
                                case 'total-ordenes':
                                    return container.querySelectorAll('#tablaLogistica tbody tr').length;
                                case 'ordenes-pendientes':
                                    return container.querySelectorAll('#tablaLogistica tbody tr[data-estado="pendiente"]').length;
                                default:
                                    return '0';
                            }
                        },
                        
                        setupAutoRefresh() {
                            // Auto-actualización cada 5 minutos
                            this.refreshInterval = setInterval(() => {
                                this.refresh();
                            }, 300000);
                        },
                        
                        refresh() {
                            loader.eventBus.dispatchEvent(new CustomEvent('dashboard:refresh'));
                            loader.reloadSection('dash_logis');
                        },
                        
                        bindEvents() {
                            // Botón de actualización manual
                            const refreshBtn = container.querySelector('#btnRefreshDashboard');
                            if (refreshBtn) {
                                refreshBtn.addEventListener('click', () => this.refresh());
                            }
                        },
                        
                        destroy() {
                            // Limpiar intervalo
                            if (this.refreshInterval) {
                                clearInterval(this.refreshInterval);
                            }
                            
                            // Destruir gráficos
                            const chartContainers = container.querySelectorAll('[data-chart]');
                            chartContainers.forEach(chartContainer => {
                                if (chartContainer._chart) {
                                    chartContainer._chart.destroy();
                                }
                            });
                        }
                    };
                    
                    module.initialize();
                    return module;
                }
            });

            // Audit Overview Module
            this.registerModule('audit_overview', {
                name: 'AuditOverviewModule',
                dependencies: [],
                init: function(container, loader) {
                    const module = {
                        container: container,
                        loader: loader,
                        
                        initialize() {
                            this.setupFilters();
                            this.setupTable();
                            this.setupExport();
                            this.setupModal();
                            this.bindEvents();
                            // Cargar datos iniciales
                            this.loadAuditData();
                        },
                        
                        setupFilters() {
                            // Configurar botón de aplicar filtros
                            const applyFiltersBtn = container.querySelector('#applyFilters');
                            if (applyFiltersBtn) {
                                applyFiltersBtn.addEventListener('click', () => this.applyFilters());
                            }
                            
                            // Configurar botón de actualizar
                            const refreshBtn = container.querySelector('#refreshTable');
                            if (refreshBtn) {
                                refreshBtn.addEventListener('click', () => this.loadAuditData());
                            }
                        },
                        
                        setupTable() {
                            const table = container.querySelector('#auditsTable');
                            if (!table) return;
                            
                            // Configurar paginación
                            this.setupPagination();
                            
                            // Configurar ordenamiento
                            const headers = table.querySelectorAll('th[data-sortable]');
                            headers.forEach(header => {
                                header.style.cursor = 'pointer';
                                header.addEventListener('click', () => this.sortTable(header));
                            });
                        },
                        
                        setupPagination() {
                            const pagination = container.querySelector('.pagination');
                            if (pagination) {
                                pagination.addEventListener('click', (e) => {
                                    if (e.target.matches('[data-page]')) {
                                        e.preventDefault();
                                        const page = e.target.getAttribute('data-page');
                                        this.loadPage(page);
                                    }
                                });
                            }
                        },
                        
                        setupExport() {
                            const exportBtn = container.querySelector('#exportAudits');
                            if (exportBtn) {
                                exportBtn.addEventListener('click', (e) => {
                                    e.preventDefault();
                                    this.exportData();
                                });
                            }
                        },
                        
                        setupModal() {
                            // Configurar eventos para abrir modales de detalle
                            container.addEventListener('click', (e) => {
                                if (e.target.matches('[data-audit-id]')) {
                                    e.preventDefault();
                                    const auditId = e.target.getAttribute('data-audit-id');
                                    this.showAuditDetail(auditId);
                                }
                            });
                        },
                        
                        applyFilters() {
                            // Aplicar filtros y recargar datos
                            this.loadAuditData();
                        },
                        
                        sortTable(header) {
                            const column = header.getAttribute('data-sort');
                            const currentSort = header.getAttribute('data-direction') || 'asc';
                            const newSort = currentSort === 'asc' ? 'desc' : 'asc';
                            
                            header.setAttribute('data-direction', newSort);
                            
                            // Actualizar indicadores visuales
                            container.querySelectorAll('th[data-sortable]').forEach(th => {
                                th.classList.remove('sort-asc', 'sort-desc');
                            });
                            header.classList.add('sort-' + newSort);
                            
                            this.loadAuditData();
                        },
                        
                        loadPage(page) {
                            const currentParams = new URLSearchParams();
                            currentParams.set('page', page);
                            this.loadAuditData(currentParams);
                        },
                        
                        loadAuditData(additionalParams = null) {
                            // Construir parámetros de filtro
                            const params = new URLSearchParams();
                            
                            // Añadir filtros actuales
                            const dateFrom = container.querySelector('#dateFrom')?.value;
                            const dateTo = container.querySelector('#dateTo')?.value;
                            const tecnico = container.querySelector('#tecnicoFilter')?.value;
                            const tipoAuditoria = container.querySelector('#tipoAuditoriaFilter')?.value;
                            
                            if (dateFrom) params.set('date_from', dateFrom);
                            if (dateTo) params.set('date_to', dateTo);
                            if (tecnico) params.set('tecnico', tecnico);
                            if (tipoAuditoria) params.set('tipo_auditoria', tipoAuditoria);
                            
                            // Añadir parámetros adicionales
                            if (additionalParams) {
                                for (const [key, value] of additionalParams) {
                                    params.set(key, value);
                                }
                            }
                            
                            // Realizar petición AJAX
                            this.fetchAuditData(params);
                        },
                        
                        fetchAuditData(params) {
                            // Añadir parámetro de sesión
                            const urlParams = new URLSearchParams(window.location.search);
                            const sessionId = urlParams.get('id_sesion');
                            if (sessionId) {
                                params.set('id_sesion', sessionId);
                            }
                            
                            const url = `get_audit_data.php?type=list&${params.toString()}`;
                            
                            fetch(url)
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        this.updateTable(data.data.audits);
                                        this.updatePagination(data.data.pagination);
                                    } else {
                                        console.error('Error loading audit data:', data.message);
                                    }
                                })
                                .catch(error => {
                                    console.error('Error fetching audit data:', error);
                                });
                        },
                        
                        updateTable(audits) {
                            const tbody = container.querySelector('#auditsTable tbody');
                            if (!tbody) return;
                            
                            tbody.innerHTML = audits.map(audit => `
                                <tr>
                                    <td>${audit.fecha}</td>
                                    <td>${audit.nombre_tecnico}</td>
                                    <td>${audit.tipo_auditoria}</td>
                                    <td>${audit.comuna}</td>
                                    <td>
                                        <div class="progress" style="height: 20px;">
                                            <div class="progress-bar ${this.getComplianceColor(audit.compliance_score)}" 
                                                 style="width: ${(audit.compliance_score * 100).toFixed(1)}%">
                                                ${(audit.compliance_score * 100).toFixed(1)}%
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <button class="btn btn-sm btn-primary" data-audit-id="${audit.id}">
                                            Ver Detalle
                                        </button>
                                    </td>
                                </tr>
                            `).join('');
                        },
                        
                        getComplianceColor(score) {
                            if (score >= 0.8) return 'bg-success';
                            if (score >= 0.6) return 'bg-warning';
                            return 'bg-danger';
                        },
                        
                        updatePagination(paginationData) {
                            const pagination = container.querySelector('.pagination');
                            if (!pagination) return;
                            
                            let paginationHTML = '';
                            
                            // Botón anterior
                            if (paginationData.has_prev) {
                                paginationHTML += `<li class="page-item"><a class="page-link" data-page="${paginationData.current_page - 1}">Anterior</a></li>`;
                            }
                            
                            // Números de página
                            for (let i = 1; i <= paginationData.total_pages; i++) {
                                const active = i === paginationData.current_page ? 'active' : '';
                                paginationHTML += `<li class="page-item ${active}"><a class="page-link" data-page="${i}">${i}</a></li>`;
                            }
                            
                            // Botón siguiente
                            if (paginationData.has_next) {
                                paginationHTML += `<li class="page-item"><a class="page-link" data-page="${paginationData.current_page + 1}">Siguiente</a></li>`;
                            }
                            
                            pagination.innerHTML = paginationHTML;
                        },
                        
                        showAuditDetail(auditId) {
                            // Cargar detalle de auditoría en modal
                            const modal = container.querySelector('#auditDetailModal');
                            if (!modal) return;
                            
                            // Mostrar loading en modal
                            const modalBody = modal.querySelector('.modal-body');
                            modalBody.innerHTML = '<div class="text-center"><div class="spinner-border"></div><p>Cargando detalle...</p></div>';
                            
                            // Abrir modal
                            const bsModal = new bootstrap.Modal(modal);
                            bsModal.show();
                            
                            // Cargar datos
                            const urlParams = new URLSearchParams(window.location.search);
                            const sessionId = urlParams.get('id_sesion');
                            const sessionParam = sessionId ? `&id_sesion=${sessionId}` : '';
                            
                            fetch(`get_audit_data.php?type=detail&id=${auditId}${sessionParam}`)
                                .then(response => response.json())
                                .then(data => {
                                    if (data.success) {
                                        this.renderAuditDetail(data.data, modalBody);
                                    } else {
                                        modalBody.innerHTML = '<div class="alert alert-danger">Error cargando detalle</div>';
                                    }
                                })
                                .catch(error => {
                                    modalBody.innerHTML = '<div class="alert alert-danger">Error de conexión</div>';
                                });
                        },
                        
                        renderAuditDetail(auditData, container) {
                            // Renderizar detalle organizado por categorías
                            const categories = auditData.categorized;
                            let html = '<div class="audit-detail-tabs">';
                            
                            // Crear tabs para cada categoría
                            html += '<ul class="nav nav-tabs">';
                            Object.keys(categories).forEach((category, index) => {
                                const active = index === 0 ? 'active' : '';
                                html += `<li class="nav-item">
                                    <a class="nav-link ${active}" data-bs-toggle="tab" href="#${category}Tab">
                                        ${this.getCategoryTitle(category)}
                                    </a>
                                </li>`;
                            });
                            html += '</ul>';
                            
                            // Contenido de tabs
                            html += '<div class="tab-content">';
                            Object.entries(categories).forEach(([category, fields], index) => {
                                const active = index === 0 ? 'show active' : '';
                                html += `<div class="tab-pane fade ${active}" id="${category}Tab">
                                    <div class="row">`;
                                
                                Object.entries(fields).forEach(([field, value]) => {
                                    html += `<div class="col-md-6 mb-2">
                                        <strong>${this.formatFieldName(field)}:</strong>
                                        <span class="ms-2 ${this.getFieldClass(value)}">${value || 'N/A'}</span>
                                    </div>`;
                                });
                                
                                html += '</div></div>';
                            });
                            html += '</div></div>';
                            
                            container.innerHTML = html;
                        },
                        
                        getCategoryTitle(category) {
                            const titles = {
                                'basica': 'Información Básica',
                                'tecnica': 'Aspectos Técnicos',
                                'herramientas': 'Herramientas',
                                'epp': 'EPP',
                                'vehiculo': 'Vehículo',
                                'mediciones': 'Mediciones',
                                'impecabilidad': 'Impecabilidad',
                                'notebook': 'Notebook'
                            };
                            return titles[category] || category;
                        },
                        
                        formatFieldName(field) {
                            return field.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
                        },
                        
                        getFieldClass(value) {
                            if (value === 'CUMPLE') return 'text-success';
                            if (value === 'NO CUMPLE') return 'text-danger';
                            if (value === 'No Aplica') return 'text-muted';
                            return '';
                        },
                        
                        exportData() {
                            // Implementar exportación de datos filtrados
                            const params = new URLSearchParams();
                            
                            // Obtener filtros actuales
                            const dateFrom = container.querySelector('#dateFrom')?.value;
                            const dateTo = container.querySelector('#dateTo')?.value;
                            const tecnico = container.querySelector('#tecnicoFilter')?.value;
                            const tipoAuditoria = container.querySelector('#tipoAuditoriaFilter')?.value;
                            
                            if (dateFrom) params.set('date_from', dateFrom);
                            if (dateTo) params.set('date_to', dateTo);
                            if (tecnico) params.set('tecnico', tecnico);
                            if (tipoAuditoria) params.set('tipo_auditoria', tipoAuditoria);
                            
                            // Crear URL de exportación
                            const exportUrl = `export_audits.php?${params.toString()}`;
                            
                            // Descargar archivo
                            window.open(exportUrl, '_blank');
                        },
                        
                        bindEvents() {
                            // Eventos personalizados
                            loader.eventBus.addEventListener('audit:refresh', () => {
                                this.loadAuditData();
                            });
                        },
                        
                        refresh() {
                            loader.reloadSection('audit_overview');
                        },
                        
                        destroy() {
                            // Limpieza al descargar el módulo
                        }
                    };
                    
                    module.initialize();
                    return module;
                }
            });

            // Más módulos pueden agregarse aquí...
        }

        /**
         * Registra un módulo para una sección
         */
        registerModule(sectionId, moduleConfig) {
            this.moduleRegistry.set(sectionId, moduleConfig);
            this.log(`📦 Módulo registrado: ${moduleConfig.name} para sección ${sectionId}`);
        }

        /**
         * Configura la navegación del sistema
         */
        setupNavigation() {
            // Delegación de eventos para mejor rendimiento
            document.addEventListener('click', (e) => {
                const menuItem = e.target.closest('[data-section]');
                if (!menuItem) return;
                
                e.preventDefault();
                const sectionId = menuItem.getAttribute('data-section');
                this.navigateToSection(sectionId);
            });
            
            // Manejar navegación por teclado
            document.addEventListener('keydown', (e) => {
                if (e.altKey && e.key >= '1' && e.key <= '9') {
                    const index = parseInt(e.key) - 1;
                    const menuItems = document.querySelectorAll('[data-section]');
                    if (menuItems[index]) {
                        e.preventDefault();
                        const sectionId = menuItems[index].getAttribute('data-section');
                        this.navigateToSection(sectionId);
                    }
                }
            });
        }

        /**
         * Configura observadores del sistema
         */
        setupObservers() {
            // Observador de visibilidad para pre-carga
            if ('IntersectionObserver' in window) {
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const container = entry.target;
                            const sectionId = container.id.replace('-container', '');
                            if (!this.state.loadedSections.has(sectionId)) {
                                this.preloadSection(sectionId);
                            }
                        }
                    });
                }, { rootMargin: '50px' });
                
                document.querySelectorAll('.section-container').forEach(container => {
                    observer.observe(container);
                });
            }
        }

        /**
         * Navega a una sección específica
         */
        async navigateToSection(sectionId) {
            this.log(`🔄 Navegando a sección: ${sectionId}`);
            
            // Desactivar sección actual
            if (this.state.activeSection && this.state.activeSection !== sectionId) {
                this.deactivateSection(this.state.activeSection);
            }
            
            // Ocultar todas las secciones
            document.querySelectorAll('.section-container').forEach(container => {
                container.style.display = 'none';
                container.classList.remove('active');
            });
            
            // Mostrar sección seleccionada
            const container = document.getElementById(sectionId + '-container');
            if (!container) {
                console.error(`Contenedor no encontrado: ${sectionId}-container`);
                return;
            }
            
            container.style.display = 'block';
            container.classList.add('active');
            
            // Cargar sección si es necesario
            if (!this.state.loadedSections.has(sectionId)) {
                await this.loadSection(sectionId);
            } else {
                // Reactivar módulo si ya está cargado
                this.activateSection(sectionId);
            }
            
            this.state.activeSection = sectionId;
            
            // Emitir evento de navegación
            this.eventBus.dispatchEvent(new CustomEvent('navigation', {
                detail: { sectionId }
            }));
        }

        /**
         * Carga una sección con reintentos
         */
        async loadSection(sectionId, attempt = 1) {
            if (this.state.loadingSections.has(sectionId)) {
                this.log(`⏳ Sección ${sectionId} ya está cargándose`);
                return;
            }
            
            this.state.loadingSections.add(sectionId);
            const container = document.getElementById(sectionId + '-container');
            
            try {
                // Mostrar indicador de carga
                this.showLoadingIndicator(container);
                
                // Construir URL
                const url = this.buildSectionUrl(sectionId);
                
                // Realizar petición con timeout
                const response = await this.fetchWithTimeout(url, this.config.loadingTimeout);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const html = await response.text();
                
                // Procesar contenido
                await this.processSectionContent(sectionId, html);
                
                // Marcar como cargada
                this.state.loadedSections.add(sectionId);
                
                // Activar sección
                this.activateSection(sectionId);
                
            } catch (error) {
                console.error(`Error cargando sección ${sectionId}:`, error);
                
                if (attempt < this.config.retryAttempts) {
                    this.log(`🔄 Reintentando carga de ${sectionId} (intento ${attempt + 1})`);
                    setTimeout(() => {
                        this.loadSection(sectionId, attempt + 1);
                    }, this.config.retryDelay * attempt);
                } else {
                    this.showErrorMessage(container, error);
                }
            } finally {
                this.state.loadingSections.delete(sectionId);
                this.hideLoadingIndicator(container);
            }
        }

        /**
         * Procesa el contenido HTML de una sección
         */
        async processSectionContent(sectionId, html) {
            const container = document.getElementById(sectionId + '-container');
            
            // Añadir clase a body para indicar que estamos en contexto de lazy-loading
            document.body.classList.add('lazy-loaded-content');
            
            // Remover TODOS los placeholders de carga (tanto antiguos como nuevos)
            const oldPlaceholders = container.querySelectorAll('.loading-placeholder');
            oldPlaceholders.forEach(placeholder => placeholder.remove());
            
            const newIndicators = container.querySelectorAll('.loading-indicator');
            newIndicators.forEach(indicator => indicator.remove());
            
            // Crear contenedor de contenido si no existe
            let contentDiv = container.querySelector('.section-content');
            if (!contentDiv) {
                contentDiv = document.createElement('div');
                contentDiv.className = 'section-content';
                container.appendChild(contentDiv);
            }
            
            // Insertar HTML
            contentDiv.innerHTML = html;
            
            // Esperar a que el DOM se actualice
            await new Promise(resolve => requestAnimationFrame(resolve));
            
            // Extraer y registrar scripts del módulo
            await this.extractAndRegisterModuleScripts(sectionId, contentDiv);
            
            // Ejecutar scripts inline generales después de que el DOM esté listo
            setTimeout(() => {
                this.executeInlineScripts(contentDiv);
                
                // Disparar evento de sección cargada
                this.eventBus.dispatchEvent(new CustomEvent('sectionLoaded', {
                    detail: { sectionId }
                }));
            }, 150);
        }

        /**
         * Extrae y registra scripts del módulo
         */
        async extractAndRegisterModuleScripts(sectionId, container) {
            const moduleScript = container.querySelector('script[data-module]');
            
            if (moduleScript) {
                try {
                    // Evaluar script del módulo en un contexto aislado
                    const moduleCode = moduleScript.textContent;
                    const moduleFactory = new Function('return ' + moduleCode)();
                    
                    // Registrar módulo dinámicamente
                    this.registerModule(sectionId, {
                        name: `${sectionId}Module`,
                        dependencies: [],
                        init: moduleFactory
                    });
                    
                    // Remover script del DOM
                    moduleScript.remove();
                } catch (error) {
                    console.error(`Error registrando módulo para ${sectionId}:`, error);
                }
            }
        }

        /**
         * Ejecuta scripts inline de una sección
         */
        executeInlineScripts(container) {
            const scripts = container.querySelectorAll('script:not([data-module])');
            
            scripts.forEach((script, index) => {
                try {
                    if (script.src) {
                        // Script externo - cargar solo si no está ya cargado
                        const existingScript = document.querySelector(`script[src="${script.src}"]`);
                        if (!existingScript) {
                            const newScript = document.createElement('script');
                            newScript.src = script.src;
                            newScript.async = false;
                            newScript.onload = () => {
                                console.log(`Script externo cargado: ${script.src}`);
                            };
                            document.head.appendChild(newScript);
                        }
                    } else {
                        // Script inline - ejecutar con manejo de errores mejorado
                        setTimeout(() => {
                            try {
                                // Envolver el script en un try-catch para evitar que errores individuales rompan todo
                                const wrappedScript = `
                                    try {
                                        ${script.textContent}
                                    } catch (error) {
                                        console.warn('Error en script inline (continuando):', error);
                                        // No lanzar el error para que continúe la ejecución
                                    }
                                `;
                                const scriptFunction = new Function(wrappedScript);
                                scriptFunction.call(window);
                            } catch (error) {
                                console.error(`Error ejecutando script inline ${index + 1}:`, error);
                                console.log(`Script content:`, script.textContent.substring(0, 200) + '...');
                            }
                        }, 200);
                    }
                } catch (error) {
                    console.error(`Error procesando script ${index + 1}:`, error);
                }
            });
        }

        /**
         * Activa una sección y su módulo
         */
        activateSection(sectionId) {
            const container = document.getElementById(sectionId + '-container');
            const moduleConfig = this.moduleRegistry.get(sectionId);
            
            if (moduleConfig && !this.state.sectionModules.has(sectionId)) {
                try {
                    // Inicializar módulo
                    const moduleInstance = moduleConfig.init(container, this);
                    this.state.sectionModules.set(sectionId, moduleInstance);
                    this.log(`✅ Módulo ${moduleConfig.name} activado`);
                } catch (error) {
                    console.error(`Error activando módulo ${sectionId}:`, error);
                }
            }
        }

        /**
         * Desactiva una sección y su módulo
         */
        deactivateSection(sectionId) {
            const moduleInstance = this.state.sectionModules.get(sectionId);
            
            if (moduleInstance && typeof moduleInstance.destroy === 'function') {
                try {
                    moduleInstance.destroy();
                    this.state.sectionModules.delete(sectionId);
                    this.log(`🔻 Módulo desactivado para sección ${sectionId}`);
                } catch (error) {
                    console.error(`Error desactivando módulo ${sectionId}:`, error);
                }
            }
        }

        /**
         * Pre-carga una sección en segundo plano
         */
        async preloadSection(sectionId) {
            if (this.state.loadedSections.has(sectionId) || 
                this.state.loadingSections.has(sectionId)) {
                return;
            }
            
            this.log(`📥 Pre-cargando sección: ${sectionId}`);
            
            try {
                const url = this.buildSectionUrl(sectionId);
                const response = await fetch(url);
                const html = await response.text();
                
                // Guardar en caché si está habilitado
                if (this.config.cacheEnabled) {
                    this.state.sectionCache.set(sectionId, {
                        html,
                        timestamp: Date.now()
                    });
                }
            } catch (error) {
                this.log(`⚠️ Error pre-cargando ${sectionId}:`, error.message);
            }
        }

        /**
         * Recarga una sección forzosamente
         */
        async reloadSection(sectionId) {
            this.log(`🔄 Recargando sección: ${sectionId}`);
            
            // Desactivar módulo actual
            this.deactivateSection(sectionId);
            
            // Limpiar estado
            this.state.loadedSections.delete(sectionId);
            this.state.sectionCache.delete(sectionId);
            
            // Recargar
            await this.loadSection(sectionId);
        }

        /**
         * Construye URL para cargar sección
         */
        buildSectionUrl(sectionId) {
            let url = this.sectionMapping[sectionId] || `get_${sectionId}.php`;
            
            // Agregar parámetros de sesión
            const urlParams = new URLSearchParams(window.location.search);
            const sessionId = urlParams.get('id_sesion');
            
            if (sessionId) {
                url += `?id_sesion=${encodeURIComponent(sessionId)}`;
            }
            
            // Cache busting
            const separator = url.includes('?') ? '&' : '?';
            url += `${separator}_t=${Date.now()}`;
            
            return url;
        }

        /**
         * Fetch con timeout
         */
        async fetchWithTimeout(url, timeout) {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), timeout);
            
            try {
                const response = await fetch(url, { signal: controller.signal });
                clearTimeout(timeoutId);
                return response;
            } catch (error) {
                clearTimeout(timeoutId);
                if (error.name === 'AbortError') {
                    throw new Error('Timeout al cargar la sección');
                }
                throw error;
            }
        }

        /**
         * Muestra indicador de carga
         */
        showLoadingIndicator(container) {
            // Primero remover cualquier indicador existente
            this.hideLoadingIndicator(container);
            
            // Crear nuevo indicador solo si no existe
            if (!container.querySelector('.loading-indicator')) {
                const indicator = document.createElement('div');
                indicator.className = 'loading-indicator';
                indicator.innerHTML = `
                    <div class="text-center p-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Cargando...</span>
                        </div>
                        <p class="mt-3">Cargando sección...</p>
                    </div>
                `;
                container.appendChild(indicator);
            }
        }

        /**
         * Oculta indicador de carga
         */
        hideLoadingIndicator(container) {
            // Remover indicadores nuevos
            const indicators = container.querySelectorAll('.loading-indicator');
            indicators.forEach(indicator => indicator.remove());
            
            // También remover placeholders antiguos del sistema anterior
            const oldPlaceholders = container.querySelectorAll('.loading-placeholder');
            oldPlaceholders.forEach(placeholder => placeholder.remove());
        }

        /**
         * Muestra mensaje de error
         */
        showErrorMessage(container, error) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message alert alert-danger m-3';
            errorDiv.innerHTML = `
                <h5><i class="bi bi-exclamation-triangle"></i> Error al cargar la sección</h5>
                <p>${error.message}</p>
                <button class="btn btn-primary btn-sm" onclick="ModularLoader.retryLoad('${container.id.replace('-container', '')}')">
                    <i class="bi bi-arrow-clockwise"></i> Reintentar
                </button>
            `;
            
            const contentDiv = container.querySelector('.section-content') || container;
            contentDiv.innerHTML = '';
            contentDiv.appendChild(errorDiv);
        }

        /**
         * Reintenta cargar una sección con error
         */
        retryLoad(sectionId) {
            const container = document.getElementById(sectionId + '-container');
            const errorMessage = container.querySelector('.error-message');
            if (errorMessage) {
                errorMessage.remove();
            }
            this.loadSection(sectionId);
        }

        /**
         * Carga sección inicial si existe
         */
        loadInitialSection() {
            const visibleSection = document.querySelector('.section-container[style*="display: block"]');
            if (visibleSection) {
                const sectionId = visibleSection.id.replace('-container', '');
                this.navigateToSection(sectionId);
            }
        }

        /**
         * Log condicional
         */
        log(...args) {
            if (this.config.debug) {
                console.log('[ModularLazyLoader]', ...args);
            }
        }

        /**
         * API Pública
         */
        
        // Obtener módulo de una sección
        getModule(sectionId) {
            return this.state.sectionModules.get(sectionId);
        }
        
        // Emitir evento global
        emit(eventName, data) {
            this.eventBus.dispatchEvent(new CustomEvent(eventName, { detail: data }));
        }
        
        // Escuchar evento global
        on(eventName, handler) {
            this.eventBus.addEventListener(eventName, handler);
        }
        
        // Remover listener
        off(eventName, handler) {
            this.eventBus.removeEventListener(eventName, handler);
        }
    }

    // Inicializar sistema cuando el DOM esté listo
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            const loader = new ModularLazyLoader();
            loader.init();
        });
    } else {
        const loader = new ModularLazyLoader();
        loader.init();
    }

})();