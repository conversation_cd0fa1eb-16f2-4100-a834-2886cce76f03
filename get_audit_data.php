<?php
/**
 * API AJAX para Datos de Auditorías
 * 
 * Proporciona endpoints RESTful para obtener datos de auditorías
 * organizados por categorías y con funcionalidades de filtrado avanzado
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Configuración de headers
header('Content-Type: application/json; charset=UTF-8');
header('Cache-Control: no-cache, must-revalidate');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST');
header('Access-Control-Allow-Headers: Content-Type');

// Incluir archivos necesarios
require_once 'DatabaseConnection.php';
require_once 'GlobalConfig.php';

// Función para enviar respuesta JSON
function sendJsonResponse($data, $success = true, $message = '', $httpCode = 200) {
    http_response_code($httpCode);
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data,
        'timestamp' => time()
    ]);
    exit;
}

// Función para validar sesión
function validateSession($sessionId, $conex) {
    if (empty($sessionId)) {
        sendJsonResponse(null, false, 'ID de sesión requerido', 401);
    }
    
    $sql = "SELECT tut.nombre, tut.email, tla.RUT, tut.id, tut.PERFIL 
            FROM TB_LOG_APP tla
            LEFT JOIN tb_user_tqw tut ON tla.RUT = tut.rut
            WHERE TOKEN = ? 
            LIMIT 1";
    
    $stmt = $conex->prepare($sql);
    $stmt->bind_param("s", $sessionId);
    $stmt->execute();
    $result = $stmt->get_result();
    
    if (!$result->fetch_assoc()) {
        sendJsonResponse(null, false, 'Sesión inválida o expirada', 401);
    }
    
    return true;
}

// Función para construir cláusula WHERE
function buildWhereClause($filters) {
    $conditions = ["1=1"];
    $params = [];
    $types = "";
    
    if (!empty($filters['date_from']) && !empty($filters['date_to'])) {
        $conditions[] = "fecha BETWEEN ? AND ?";
        $params[] = $filters['date_from'];
        $params[] = $filters['date_to'];
        $types .= "ss";
    }
    
    if (!empty($filters['tecnico'])) {
        $conditions[] = "nombre_tecnico = ?";
        $params[] = $filters['tecnico'];
        $types .= "s";
    }
    
    if (!empty($filters['tipo_auditoria'])) {
        $conditions[] = "tipo_auditoria = ?";
        $params[] = $filters['tipo_auditoria'];
        $types .= "s";
    }
    
    if (!empty($filters['supervisor'])) {
        $conditions[] = "supervisor_auditor = ?";
        $params[] = $filters['supervisor'];
        $types .= "s";
    }
    
    if (!empty($filters['comuna'])) {
        $conditions[] = "comuna = ?";
        $params[] = $filters['comuna'];
        $types .= "s";
    }
    
    return [
        'where' => implode(' AND ', $conditions),
        'params' => $params,
        'types' => $types
    ];
}

try {
    // Inicializar conexión
    $db = DatabaseConnection::getInstance();
    $conex = $db->getConnection();
    
    // Obtener parámetros
    $type = $_GET['type'] ?? 'summary';
    $sessionId = $_GET['id_sesion'] ?? '';
    $auditId = intval($_GET['id'] ?? 0);
    $category = $_GET['category'] ?? '';
    
    // Validar sesión
    validateSession($sessionId, $conex);
    
    // Procesar según tipo de solicitud
    switch ($type) {
        case 'summary':
            handleSummaryRequest($conex);
            break;
            
        case 'list':
            handleListRequest($conex);
            break;
            
        case 'detail':
            handleDetailRequest($conex, $auditId);
            break;
            
        case 'category':
            handleCategoryRequest($conex, $auditId, $category);
            break;
            
        case 'chart':
            handleChartRequest($conex);
            break;
            
        case 'filters':
            handleFiltersRequest($conex);
            break;
            
        default:
            sendJsonResponse(null, false, 'Tipo de solicitud no válido', 400);
            break;
    }
    
} catch (Exception $e) {
    error_log("Error en get_audit_data.php: " . $e->getMessage());
    sendJsonResponse(null, false, 'Error interno del servidor', 500);
}

/**
 * Maneja solicitudes de datos de resumen
 */
function handleSummaryRequest($conex) {
    $filters = [
        'date_from' => $_GET['date_from'] ?? '',
        'date_to' => $_GET['date_to'] ?? '',
        'tecnico' => $_GET['tecnico'] ?? '',
        'tipo_auditoria' => $_GET['tipo_auditoria'] ?? '',
        'supervisor' => $_GET['supervisor'] ?? '',
        'comuna' => $_GET['comuna'] ?? ''
    ];
    
    $whereData = buildWhereClause($filters);
    
    // Consulta principal de resumen
    $summaryQuery = "
        SELECT 
            COUNT(*) as total_auditorias,
            COUNT(DISTINCT nombre_tecnico) as total_tecnicos,
            COUNT(DISTINCT supervisor_auditor) as total_supervisores,
            COUNT(DISTINCT comuna) as total_comunas,
            
            -- Contadores de cumplimiento por categoría
            SUM(CASE WHEN insp_estado_acometida = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN insp_estado_conectores = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN insp_estado_cableado = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_tx = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_rx = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_snr = 'CUMPLE' THEN 1 ELSE 0 END) as cumplimientos_totales,
            
            SUM(CASE WHEN insp_estado_acometida = 'NO CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN insp_estado_conectores = 'NO CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN insp_estado_cableado = 'NO CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_tx = 'NO CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_rx = 'NO CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_snr = 'NO CUMPLE' THEN 1 ELSE 0 END) as incumplimientos_totales
            
        FROM tb_auditorias_terreno 
        WHERE {$whereData['where']}";
    
    $stmt = $conex->prepare($summaryQuery);
    if ($whereData['params']) {
        $stmt->bind_param($whereData['types'], ...$whereData['params']);
    }
    $stmt->execute();
    $summary = $stmt->get_result()->fetch_assoc();
    
    // Calcular tasas de cumplimiento
    $total_checks = $summary['cumplimientos_totales'] + $summary['incumplimientos_totales'];
    $compliance_rate = $total_checks > 0 ? ($summary['cumplimientos_totales'] / $total_checks) * 100 : 0;
    
    // Obtener distribución por tipos
    $typesQuery = "
        SELECT tipo_auditoria, COUNT(*) as cantidad
        FROM tb_auditorias_terreno 
        WHERE {$whereData['where']}
        GROUP BY tipo_auditoria
        ORDER BY cantidad DESC";
    
    $stmt = $conex->prepare($typesQuery);
    if ($whereData['params']) {
        $stmt->bind_param($whereData['types'], ...$whereData['params']);
    }
    $stmt->execute();
    $auditTypes = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Obtener tendencias por mes
    $trendsQuery = "
        SELECT 
            DATE_FORMAT(fecha, '%Y-%m') as mes,
            COUNT(*) as cantidad,
            AVG((CASE WHEN insp_estado_acometida = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN insp_estado_conectores = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN insp_estado_cableado = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN estado_cpe_tx = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN estado_cpe_rx = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN estado_cpe_snr = 'CUMPLE' THEN 1 ELSE 0 END) / 6.0) * 100 as compliance_avg
        FROM tb_auditorias_terreno 
        WHERE {$whereData['where']}
        GROUP BY DATE_FORMAT(fecha, '%Y-%m')
        ORDER BY mes DESC
        LIMIT 6";
    
    $stmt = $conex->prepare($trendsQuery);
    if ($whereData['params']) {
        $stmt->bind_param($whereData['types'], ...$whereData['params']);
    }
    $stmt->execute();
    $trends = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $result = array_merge($summary, [
        'compliance_rate' => round($compliance_rate, 2),
        'audit_types' => $auditTypes,
        'trends' => array_reverse($trends)
    ]);
    
    sendJsonResponse($result);
}

/**
 * Maneja solicitudes de listado de auditorías
 */
function handleListRequest($conex) {
    $page = max(1, intval($_GET['page'] ?? 1));
    $limit = min(100, max(10, intval($_GET['limit'] ?? 25)));
    $offset = ($page - 1) * $limit;
    
    $filters = [
        'date_from' => $_GET['date_from'] ?? '',
        'date_to' => $_GET['date_to'] ?? '',
        'tecnico' => $_GET['tecnico'] ?? '',
        'tipo_auditoria' => $_GET['tipo_auditoria'] ?? '',
        'supervisor' => $_GET['supervisor'] ?? '',
        'comuna' => $_GET['comuna'] ?? ''
    ];
    
    $whereData = buildWhereClause($filters);
    
    // Consulta principal con paginación
    $listQuery = "
        SELECT 
            id, fecha, supervisor_auditor, nombre_tecnico, tipo_auditoria, 
            rut, numero_orden, direccion, comuna, tipo_actividad, tipo_servicio,
            observaciones,
            -- Cálculo de score de cumplimiento
            (CASE WHEN insp_estado_acometida = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN insp_estado_conectores = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN insp_estado_cableado = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN estado_cpe_tx = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN estado_cpe_rx = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN estado_cpe_snr = 'CUMPLE' THEN 1 ELSE 0 END) / 6.0 as compliance_score,
             
            -- Indicadores adicionales
            CASE WHEN evidencia_fotografica IS NOT NULL AND evidencia_fotografica != '' THEN 1 ELSE 0 END as has_photos,
            CASE WHEN observaciones IS NOT NULL AND observaciones != '' THEN 1 ELSE 0 END as has_observations
            
        FROM tb_auditorias_terreno 
        WHERE {$whereData['where']}
        ORDER BY fecha DESC, id DESC
        LIMIT $limit OFFSET $offset";
    
    $stmt = $conex->prepare($listQuery);
    if ($whereData['params']) {
        $stmt->bind_param($whereData['types'], ...$whereData['params']);
    }
    $stmt->execute();
    $audits = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Contar total para paginación
    $countQuery = "SELECT COUNT(*) as total FROM tb_auditorias_terreno WHERE {$whereData['where']}";
    $stmt = $conex->prepare($countQuery);
    if ($whereData['params']) {
        $stmt->bind_param($whereData['types'], ...$whereData['params']);
    }
    $stmt->execute();
    $total = $stmt->get_result()->fetch_assoc()['total'];
    
    $result = [
        'audits' => $audits,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => ceil($total / $limit),
            'total_records' => $total,
            'per_page' => $limit,
            'has_next' => $page < ceil($total / $limit),
            'has_prev' => $page > 1
        ]
    ];
    
    sendJsonResponse($result);
}

/**
 * Maneja solicitudes de detalle de auditoría específica
 */
function handleDetailRequest($conex, $auditId) {
    if ($auditId <= 0) {
        sendJsonResponse(null, false, 'ID de auditoría inválido', 400);
    }
    
    // Obtener todos los campos de la auditoría
    $detailQuery = "SELECT * FROM tb_auditorias_terreno WHERE id = ?";
    $stmt = $conex->prepare($detailQuery);
    $stmt->bind_param("i", $auditId);
    $stmt->execute();
    $audit = $stmt->get_result()->fetch_assoc();
    
    if (!$audit) {
        sendJsonResponse(null, false, 'Auditoría no encontrada', 404);
    }
    
    // Organizar datos por categorías
    $categorizedData = [
        'basica' => [
            'id' => $audit['id'],
            'fecha' => $audit['fecha'],
            'supervisor_auditor' => $audit['supervisor_auditor'],
            'nombre_tecnico' => $audit['nombre_tecnico'],
            'tipo_auditoria' => $audit['tipo_auditoria'],
            'rut' => $audit['rut'],
            'numero_orden' => $audit['numero_orden'],
            'direccion' => $audit['direccion'],
            'comuna' => $audit['comuna'],
            'tipo_actividad' => $audit['tipo_actividad'],
            'tipo_servicio' => $audit['tipo_servicio']
        ],
        'tecnica' => [],
        'herramientas' => [],
        'epp' => [],
        'vehiculo' => [],
        'mediciones' => [],
        'impecabilidad' => [],
        'notebook' => []
    ];
    
    // Clasificar campos por categorías
    foreach ($audit as $field => $value) {
        if (strpos($field, 'insp_') === 0 || strpos($field, 'estado_cpe_') === 0) {
            $categorizedData['tecnica'][$field] = $value;
        } elseif (strpos($field, 'herramientas_') === 0) {
            $categorizedData['herramientas'][$field] = $value;
        } elseif (strpos($field, 'epp_') === 0) {
            $categorizedData['epp'][$field] = $value;
        } elseif (strpos($field, 'vehiculo_') === 0) {
            $categorizedData['vehiculo'][$field] = $value;
        } elseif (strpos($field, 'mediciones_') === 0) {
            $categorizedData['mediciones'][$field] = $value;
        } elseif (strpos($field, 'impecabilidad_') === 0 || strpos($field, 'presentacion_') === 0) {
            $categorizedData['impecabilidad'][$field] = $value;
        } elseif (strpos($field, 'notebook_') === 0 || strpos($field, 'equipo_') === 0) {
            // Campos relacionados con notebook van a su propia categoría
            $categorizedData['notebook'][$field] = $value;
        } elseif (!array_key_exists($field, $categorizedData['basica'])) {
            // El resto de campos adicionales van a información básica en lugar de "otros"
            $categorizedData['basica'][$field] = $value;
        }
    }
    
    // Calcular estadísticas de cumplimiento
    $complianceStats = calculateComplianceStats($audit);
    
    $result = [
        'audit' => $audit,
        'categorized' => $categorizedData,
        'compliance_stats' => $complianceStats
    ];
    
    sendJsonResponse($result);
}

/**
 * Maneja solicitudes de datos de categoría específica
 */
function handleCategoryRequest($conex, $auditId, $category) {
    if ($auditId <= 0) {
        sendJsonResponse(null, false, 'ID de auditoría inválido', 400);
    }
    
    if (empty($category)) {
        sendJsonResponse(null, false, 'Categoría no especificada', 400);
    }
    
    // Mapear categorías a prefijos de campos
    $categoryMap = [
        'tecnica' => ['insp_', 'estado_cpe_'],
        'herramientas' => ['herramientas_'],
        'epp' => ['epp_'],
        'vehiculo' => ['vehiculo_'],
        'mediciones' => ['mediciones_'],
        'impecabilidad' => ['impecabilidad_', 'presentacion_'],
        'notebook' => ['notebook_', 'equipo_'],
        'ot_digital' => ['ot_digital_']
    ];
    
    if (!array_key_exists($category, $categoryMap)) {
        sendJsonResponse(null, false, 'Categoría no válida', 400);
    }
    
    // Obtener campos específicos de la categoría
    $fieldsQuery = "SELECT * FROM tb_auditorias_terreno WHERE id = ?";
    $stmt = $conex->prepare($fieldsQuery);
    $stmt->bind_param("i", $auditId);
    $stmt->execute();
    $audit = $stmt->get_result()->fetch_assoc();
    
    if (!$audit) {
        sendJsonResponse(null, false, 'Auditoría no encontrada', 404);
    }
    
    // Filtrar campos por categoría
    $categoryData = [];
    foreach ($audit as $field => $value) {
        foreach ($categoryMap[$category] as $prefix) {
            if (strpos($field, $prefix) === 0) {
                $categoryData[$field] = $value;
                break;
            }
        }
    }
    
    sendJsonResponse($categoryData);
}

/**
 * Maneja solicitudes de datos para gráficos
 */
function handleChartRequest($conex) {
    $filters = [
        'date_from' => $_GET['date_from'] ?? '',
        'date_to' => $_GET['date_to'] ?? '',
        'tecnico' => $_GET['tecnico'] ?? '',
        'tipo_auditoria' => $_GET['tipo_auditoria'] ?? ''
    ];
    
    $whereData = buildWhereClause($filters);
    
    // Datos para gráfico de cumplimiento por categoría
    $categoryComplianceQuery = "
        SELECT 
            'Inspección' as category,
            SUM(CASE WHEN insp_estado_acometida = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN insp_estado_conectores = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN insp_estado_cableado = 'CUMPLE' THEN 1 ELSE 0 END) as cumple,
            COUNT(*) * 3 as total
        FROM tb_auditorias_terreno WHERE {$whereData['where']}
        
        UNION ALL
        
        SELECT 
            'CPE' as category,
            SUM(CASE WHEN estado_cpe_tx = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_rx = 'CUMPLE' THEN 1 ELSE 0 END) +
            SUM(CASE WHEN estado_cpe_snr = 'CUMPLE' THEN 1 ELSE 0 END) as cumple,
            COUNT(*) * 3 as total
        FROM tb_auditorias_terreno WHERE {$whereData['where']}";
    
    $stmt = $conex->prepare($categoryComplianceQuery);
    if ($whereData['params']) {
        $stmt->bind_param($whereData['types'], ...$whereData['params']);
    }
    $stmt->execute();
    $categoryCompliance = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    // Datos por técnico
    $technicianDataQuery = "
        SELECT 
            nombre_tecnico,
            COUNT(*) as total_auditorias,
            AVG((CASE WHEN insp_estado_acometida = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN insp_estado_conectores = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN insp_estado_cableado = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN estado_cpe_tx = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN estado_cpe_rx = 'CUMPLE' THEN 1 ELSE 0 END +
                 CASE WHEN estado_cpe_snr = 'CUMPLE' THEN 1 ELSE 0 END) / 6.0) * 100 as compliance_avg
        FROM tb_auditorias_terreno 
        WHERE {$whereData['where']}
        GROUP BY nombre_tecnico
        ORDER BY total_auditorias DESC
        LIMIT 10";
    
    $stmt = $conex->prepare($technicianDataQuery);
    if ($whereData['params']) {
        $stmt->bind_param($whereData['types'], ...$whereData['params']);
    }
    $stmt->execute();
    $technicianData = $stmt->get_result()->fetch_all(MYSQLI_ASSOC);
    
    $result = [
        'category_compliance' => $categoryCompliance,
        'technician_data' => $technicianData
    ];
    
    sendJsonResponse($result);
}

/**
 * Maneja solicitudes de opciones para filtros
 */
function handleFiltersRequest($conex) {
    $filterType = $_GET['filter_type'] ?? 'all';
    
    $result = [];
    
    if ($filterType === 'all' || $filterType === 'tecnicos') {
        $tecnicosQuery = "SELECT DISTINCT nombre_tecnico FROM tb_auditorias_terreno WHERE nombre_tecnico IS NOT NULL ORDER BY nombre_tecnico";
        $result['tecnicos'] = $conex->query($tecnicosQuery)->fetch_all(MYSQLI_ASSOC);
    }
    
    if ($filterType === 'all' || $filterType === 'tipos') {
        $tiposQuery = "SELECT DISTINCT tipo_auditoria FROM tb_auditorias_terreno WHERE tipo_auditoria IS NOT NULL ORDER BY tipo_auditoria";
        $result['tipos_auditoria'] = $conex->query($tiposQuery)->fetch_all(MYSQLI_ASSOC);
    }
    
    if ($filterType === 'all' || $filterType === 'supervisores') {
        $supervisoresQuery = "SELECT DISTINCT supervisor_auditor FROM tb_auditorias_terreno WHERE supervisor_auditor IS NOT NULL ORDER BY supervisor_auditor";
        $result['supervisores'] = $conex->query($supervisoresQuery)->fetch_all(MYSQLI_ASSOC);
    }
    
    if ($filterType === 'all' || $filterType === 'comunas') {
        $comunasQuery = "SELECT DISTINCT comuna FROM tb_auditorias_terreno WHERE comuna IS NOT NULL ORDER BY comuna";
        $result['comunas'] = $conex->query($comunasQuery)->fetch_all(MYSQLI_ASSOC);
    }
    
    sendJsonResponse($result);
}

/**
 * Calcula estadísticas de cumplimiento para una auditoría
 */
function calculateComplianceStats($audit) {
    $categories = [
        'inspeccion' => ['insp_estado_acometida', 'insp_estado_conectores', 'insp_estado_cableado'],
        'cpe' => ['estado_cpe_tx', 'estado_cpe_rx', 'estado_cpe_snr'],
        'velocidad' => ['estado_cpe_velocidad_bajada', 'estado_cpe_velocidad_subida'],
        'impecabilidad' => []  // Se llenarán dinámicamente abajo
    ];
    
    $stats = [];
    
    // Buscar campos de impecabilidad dinámicamente
    foreach ($audit as $field => $value) {
        if (strpos($field, 'impecabilidad_') === 0 || strpos($field, 'presentacion_') === 0) {
            $categories['impecabilidad'][] = $field;
        }
    }
    
    foreach ($categories as $category => $fields) {
        $cumple = 0;
        $no_cumple = 0;
        $no_aplica = 0;
        $total = 0;
        
        foreach ($fields as $field) {
            if (isset($audit[$field]) && !empty($audit[$field])) {
                $total++;
                switch ($audit[$field]) {
                    case 'CUMPLE':
                        $cumple++;
                        break;
                    case 'NO CUMPLE':
                        $no_cumple++;
                        break;
                    case 'No Aplica':
                        $no_aplica++;
                        break;
                }
            }
        }
        
        $stats[$category] = [
            'cumple' => $cumple,
            'no_cumple' => $no_cumple,
            'no_aplica' => $no_aplica,
            'total' => $total,
            'compliance_rate' => $total > 0 ? ($cumple / $total) * 100 : 0
        ];
    }
    
    return $stats;
}
?>