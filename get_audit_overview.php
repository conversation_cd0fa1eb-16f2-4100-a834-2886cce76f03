<?php
/**
 * Sistema de Visualización de Auditorías - Sección para Lazy Loading
 * 
 * Proporciona el contenido HTML para la sección de auditorías
 * que se carga dinámicamente en el dashboard principal
 * 
 * @version 1.0.0
 * <AUTHOR> Development Team
 */

// Evitar caché
header('Cache-Control: no-store, no-cache, must-revalidate, max-age=0');
header('Cache-Control: post-check=0, pre-check=0', false);
header('Pragma: no-cache');

// Iniciar sesión si es necesario
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Incluir archivos de configuración
require_once 'DatabaseConnection.php';
require_once 'GlobalConfig.php';

// Obtener parámetros
$sesion = $_GET['id_sesion'] ?? '';

// Inicialización
$config = GlobalConfig::getInstance();

try {
    $db = DatabaseConnection::getInstance();
    $conex = $db->getConnection();
    
    register_shutdown_function(function() use ($db) {
        if ($db !== null) {
            $db->cleanup();
        }
    });
    
} catch (Exception $e) {
    error_log("Error de conexión: " . $e->getMessage());
    echo '<div class="alert alert-danger">Error de conexión: Por favor, contacte al administrador</div>';
    exit;
}

// Validar sesión de usuario
if (empty($sesion)) {
    echo '<div class="alert alert-warning">Sesión no válida</div>';
    exit;
}

$sql3 = "SELECT tut.nombre, tut.email, tla.RUT, tut.id, tut.PERFIL, tut.PERFIL2,
            tut2.id as id_supervisor, tut.rut AS RUT_OR, tut.nombre_short
        FROM TB_LOG_APP tla
        LEFT JOIN tb_user_tqw tut ON tla.RUT = tut.rut
        LEFT JOIN tb_user_tqw tut2 ON tut2.email = tut.correo_super
        WHERE TOKEN = ? 
        LIMIT 1";

$stmt = $conex->prepare($sql3);
if ($stmt === false) {
    echo '<div class="alert alert-danger">Error en la validación de sesión</div>';
    exit;
}

$stmt->bind_param("s", $sesion);

if (!$stmt->execute()) {
    echo '<div class="alert alert-danger">Error en la validación de sesión</div>';
    exit;
}

$result = $stmt->get_result();
$row = $result->fetch_assoc();

if (!$row) {
    echo '<div class="alert alert-warning">Sesión inválida o expirada</div>';
    exit;
}

$nombre_user = $row['nombre_short'];
$email = $row['email'];
$perfil = $row['PERFIL'];
$perfil2 = $row['PERFIL2'];
$rut_ejecut = $row['RUT_OR'];

// Obtener parámetros de filtros con valores por defecto
$date_from = $_GET['date_from'] ?? date('Y-m-d', strtotime('-30 days'));
$date_to = $_GET['date_to'] ?? date('Y-m-d');
$tecnico = $_GET['tecnico'] ?? '';
$tipo_auditoria = $_GET['tipo_auditoria'] ?? '';

// Obtener datos de resumen para las tarjetas
$whereConditions = ["1=1"];
$params = [];
$types = "";

if ($date_from && $date_to) {
    $whereConditions[] = "fecha BETWEEN ? AND ?";
    $params[] = $date_from;
    $params[] = $date_to;
    $types .= "ss";
}

if ($tecnico) {
    $whereConditions[] = "nombre_tecnico = ?";
    $params[] = $tecnico;
    $types .= "s";
}

if ($tipo_auditoria) {
    $whereConditions[] = "tipo_auditoria = ?";
    $params[] = $tipo_auditoria;
    $types .= "s";
}

$whereClause = implode(' AND ', $whereConditions);

// Consulta de resumen
$summaryQuery = "
    SELECT 
        COUNT(*) as total_auditorias,
        COUNT(DISTINCT nombre_tecnico) as total_tecnicos,
        COUNT(DISTINCT supervisor_auditor) as total_supervisores,
        AVG((CASE WHEN insp_estado_acometida = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN insp_estado_conectores = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN insp_estado_cableado = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN estado_cpe_tx = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN estado_cpe_rx = 'CUMPLE' THEN 1 ELSE 0 END +
             CASE WHEN estado_cpe_snr = 'CUMPLE' THEN 1 ELSE 0 END) / 6.0) * 100 as compliance_avg
    FROM tb_auditorias_terreno 
    WHERE $whereClause";

$stmt = $conex->prepare($summaryQuery);
if ($params) {
    $stmt->bind_param($types, ...$params);
}
$stmt->execute();
$summary = $stmt->get_result()->fetch_assoc();

// Obtener opciones para filtros
$tecnicosQuery = "SELECT DISTINCT nombre_tecnico FROM tb_auditorias_terreno WHERE nombre_tecnico IS NOT NULL ORDER BY nombre_tecnico";
$tecnicos = $conex->query($tecnicosQuery)->fetch_all(MYSQLI_ASSOC);

$tiposQuery = "SELECT DISTINCT tipo_auditoria FROM tb_auditorias_terreno WHERE tipo_auditoria IS NOT NULL ORDER BY tipo_auditoria";
$tipos = $conex->query($tiposQuery)->fetch_all(MYSQLI_ASSOC);

?>

<!-- Estilos específicos para la sección de auditorías -->
<style>
.audit-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
}

.audit-filters {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.audit-table-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.compliance-bar {
    height: 25px;
    border-radius: 12px;
    overflow: hidden;
    background-color: #e9ecef;
}

.compliance-progress {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    transition: width 0.3s ease;
}

.audit-detail-tabs .nav-tabs {
    border-bottom: 2px solid #dee2e6;
}

.audit-detail-tabs .nav-link {
    border: none;
    color: #495057;
    font-weight: 500;
}

.audit-detail-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
}

.audit-detail-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.field-value.text-success {
    background-color: #d4edda;
    padding: 2px 8px;
    border-radius: 4px;
}

.field-value.text-danger {
    background-color: #f8d7da;
    padding: 2px 8px;
    border-radius: 4px;
}

.field-value.text-muted {
    background-color: #f8f9fa;
    padding: 2px 8px;
    border-radius: 4px;
}
</style>

<!-- Contenido principal de la sección de auditorías -->
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h2 class="mb-4">
                <i class="bi bi-clipboard-check me-2"></i>
                Sistema de Auditorías
            </h2>
        </div>
    </div>

    <!-- Tarjetas de resumen -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="audit-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="h4 mb-0"><?php echo number_format($summary['total_auditorias']); ?></h3>
                        <p class="mb-0">Total Auditorías</p>
                    </div>
                    <i class="bi bi-clipboard-data fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="audit-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="h4 mb-0"><?php echo number_format($summary['total_tecnicos']); ?></h3>
                        <p class="mb-0">Técnicos</p>
                    </div>
                    <i class="bi bi-people fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="audit-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="h4 mb-0"><?php echo number_format($summary['total_supervisores']); ?></h3>
                        <p class="mb-0">Supervisores</p>
                    </div>
                    <i class="bi bi-person-badge fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="audit-card">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="h4 mb-0"><?php echo number_format($summary['compliance_avg'], 1); ?>%</h3>
                        <p class="mb-0">Cumplimiento Promedio</p>
                    </div>
                    <i class="bi bi-graph-up fs-1 opacity-75"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- Filtros -->
    <div class="audit-filters">
        <h5 class="mb-3">Filtros</h5>
        <div class="row">
            <div class="col-md-3">
                <label for="dateFrom" class="form-label">Fecha Desde</label>
                <input type="date" class="form-control" id="dateFrom" value="<?php echo $date_from; ?>">
            </div>
            <div class="col-md-3">
                <label for="dateTo" class="form-label">Fecha Hasta</label>
                <input type="date" class="form-control" id="dateTo" value="<?php echo $date_to; ?>">
            </div>
            <div class="col-md-3">
                <label for="tecnicoFilter" class="form-label">Técnico</label>
                <select class="form-select" id="tecnicoFilter">
                    <option value="">Todos los técnicos</option>
                    <?php foreach ($tecnicos as $t): ?>
                        <option value="<?php echo htmlspecialchars($t['nombre_tecnico']); ?>" 
                                <?php echo $tecnico === $t['nombre_tecnico'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($t['nombre_tecnico']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-3">
                <label for="tipoAuditoriaFilter" class="form-label">Tipo de Auditoría</label>
                <select class="form-select" id="tipoAuditoriaFilter">
                    <option value="">Todos los tipos</option>
                    <?php foreach ($tipos as $t): ?>
                        <option value="<?php echo htmlspecialchars($t['tipo_auditoria']); ?>"
                                <?php echo $tipo_auditoria === $t['tipo_auditoria'] ? 'selected' : ''; ?>>
                            <?php echo htmlspecialchars($t['tipo_auditoria']); ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
        </div>
        <div class="row mt-3">
            <div class="col-12">
                <button type="button" class="btn btn-primary me-2" id="applyFilters">
                    <i class="bi bi-funnel"></i> Aplicar Filtros
                </button>
                <button type="button" class="btn btn-success" id="exportAudits">
                    <i class="bi bi-download"></i> Exportar
                </button>
            </div>
        </div>
    </div>

    <!-- Tabla de auditorías -->
    <div class="audit-table-container">
        <div class="d-flex justify-content-between align-items-center mb-3">
            <h5 class="mb-0">Listado de Auditorías</h5>
            <div class="d-flex gap-2">
                <button class="btn btn-outline-primary btn-sm" id="refreshTable">
                    <i class="bi bi-arrow-clockwise"></i> Actualizar
                </button>
            </div>
        </div>
        
        <div class="table-responsive">
            <table class="table table-hover" id="auditsTable">
                <thead class="table-dark">
                    <tr>
                        <th data-sortable data-sort="fecha">Fecha</th>
                        <th data-sortable data-sort="nombre_tecnico">Técnico</th>
                        <th data-sortable data-sort="tipo_auditoria">Tipo</th>
                        <th data-sortable data-sort="comuna">Comuna</th>
                        <th>Cumplimiento</th>
                        <th>Acciones</th>
                    </tr>
                </thead>
                <tbody>
                    <!-- Los datos se cargarán dinámicamente -->
                    <tr>
                        <td colspan="6" class="text-center py-4">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Cargando...</span>
                            </div>
                            <p class="mt-2">Cargando datos de auditorías...</p>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Paginación -->
        <nav aria-label="Paginación de auditorías">
            <ul class="pagination justify-content-center">
                <!-- La paginación se generará dinámicamente -->
            </ul>
        </nav>
    </div>
</div>

<!-- Modal para detalle de auditoría -->
<div class="modal fade" id="auditDetailModal" tabindex="-1" aria-labelledby="auditDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="auditDetailModalLabel">
                    <i class="bi bi-clipboard-data me-2"></i>
                    Detalle de Auditoría
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- El contenido se cargará dinámicamente -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
            </div>
        </div>
    </div>
</div>

<!-- Cargar Chart.js si no está disponible -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
// Configuración inicial para la sección de auditorías
(function initializeAuditOverviewSection() {
    console.log('Inicializando sección de auditorías...');
    
    // Datos iniciales
    const sessionId = '<?php echo $sesion; ?>';
    
    // Función para inicializar gráficos básicos
    function initializeBasicCharts() {
        try {
            // Solo crear gráficos simples para demostrar funcionalidad
            console.log('Inicializando gráficos básicos...');
            
            // Aquí podrías agregar gráficos básicos si es necesario
            // Por ahora, solo mostraremos que la sección está funcionando
            
        } catch (error) {
            console.warn('Error inicializando gráficos:', error);
        }
    }
    
    // Función para configurar eventos básicos
    function setupBasicEventHandlers() {
        try {
            // Configurar filtros si existen
            const applyFiltersBtn = document.getElementById('applyFilters');
            if (applyFiltersBtn) {
                applyFiltersBtn.addEventListener('click', function() {
                    console.log('Aplicando filtros...');
                    // Lógica de filtros aquí
                });
            }
            
            // Configurar exportación si existe
            const exportBtn = document.getElementById('exportAudits');
            if (exportBtn) {
                exportBtn.addEventListener('click', function() {
                    console.log('Exportando auditorías...');
                    // Lógica de exportación aquí
                });
            }
            
            // Configurar actualización si existe
            const refreshBtn = document.getElementById('refreshTable');
            if (refreshBtn) {
                refreshBtn.addEventListener('click', function() {
                    console.log('Actualizando tabla...');
                    // Lógica de actualización aquí
                });
            }
            
        } catch (error) {
            console.warn('Error configurando eventos:', error);
        }
    }
    
    // Ejecutar inicialización con delay para asegurar que el DOM esté listo
    setTimeout(function() {
        initializeBasicCharts();
        setupBasicEventHandlers();
        console.log('Sección de auditorías inicializada correctamente');
    }, 300);
    
})();
</script>